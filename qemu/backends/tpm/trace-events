# See docs/devel/tracing.rst for syntax documentation.

# tpm_passthrough.c
tpm_passthrough_handle_request(void *cmd) "processing command %p"
tpm_passthrough_reset(void) "reset"

# tpm_util.c
tpm_util_get_buffer_size_hdr_len(uint32_t len, size_t expected) "tpm_resp->hdr.len = %u, expected = %zu"
tpm_util_get_buffer_size_len(uint32_t len, size_t expected) "tpm_resp->len = %u, expected = %zu"
tpm_util_get_buffer_size_hdr_len2(uint32_t len, size_t expected) "tpm2_resp->hdr.len = %u, expected = %zu"
tpm_util_get_buffer_size_len2(uint32_t len, size_t expected) "tpm2_resp->len = %u, expected = %zu"
tpm_util_get_buffer_size(size_t len) "buffersize of device: %zu"
tpm_util_show_buffer_header(const char *direction, size_t len) "direction: %s len: %zu"
tpm_util_show_buffer_content(const char *buf) "%s"

# tpm_emulator.c
tpm_emulator_set_locality(uint8_t locty) "setting locality to %d"
tpm_emulator_handle_request(void) "processing TPM command"
tpm_emulator_probe_caps(uint32_t caps) "capabilities: 0x%x"
tpm_emulator_set_buffer_size(uint32_t buffersize, uint32_t minsize, uint32_t maxsize) "buffer size: %u, min: %u, max: %u"
tpm_emulator_startup_tpm_resume(bool is_resume, size_t buffersize) "is_resume: %d, buffer size: %zu"
tpm_emulator_get_tpm_established_flag(uint8_t flag) "got established flag: %d"
tpm_emulator_cancel_cmd_not_supt(void) "Backend does not support CANCEL_TPM_CMD"
tpm_emulator_lock_storage_cmd_not_supt(void) "Backend does not support LOCK_STORAGE"
tpm_emulator_vm_state_change(int running, int state) "state change to running %d state %d"
tpm_emulator_handle_device_opts_tpm12(void) "TPM Version 1.2"
tpm_emulator_handle_device_opts_tpm2(void) "TPM Version 2"
tpm_emulator_handle_device_opts_unspec(void) "TPM Version Unspecified"
tpm_emulator_handle_device_opts_startup_error(void) "Startup error"
tpm_emulator_get_state_blob(uint8_t type, uint32_t size, uint32_t flags) "got state blob type %d, %u bytes, flags 0x%08x"
tpm_emulator_set_state_blob(uint8_t type, uint32_t size, uint32_t flags) "set state blob type %d, %u bytes, flags 0x%08x"
tpm_emulator_set_state_blobs(void) "setting state blobs"
tpm_emulator_set_state_blobs_error(const char *msg) "error while setting state blobs: %s"
tpm_emulator_set_state_blobs_done(void) "Done setting state blobs"
tpm_emulator_pre_save(void) ""
tpm_emulator_inst_init(void) ""
