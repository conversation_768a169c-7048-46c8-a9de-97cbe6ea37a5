# All ubuntu-22.04 jobs should run successfully in an environment
# setup by the scripts/ci/setup/ubuntu/build-environment.yml task
# "Install basic packages to build QEMU on Ubuntu 22.04"

ubuntu-22.04-aarch32-all:
 extends: .custom_runner_template
 needs: []
 stage: build
 tags:
 - ubuntu_22.04
 - aarch32
 rules:
 - if: '$CI_PROJECT_NAMESPACE == "qemu-project" && $CI_COMMIT_BRANCH =~ /^staging/'
   when: manual
   allow_failure: true
 - if: "$AARCH32_RUNNER_AVAILABLE"
   when: manual
   allow_failure: true
 script:
 - mkdir build
 - cd build
 - ../configure --cross-prefix=arm-linux-gnueabihf-
   || { cat config.log meson-logs/meson-log.txt; exit 1; }
 - make --output-sync -j`nproc --ignore=40`
 - make --output-sync -j`nproc --ignore=40` check
