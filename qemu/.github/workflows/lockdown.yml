# Configuration for Repo Lockdown - https://github.com/dessant/repo-lockdown

name: 'Repo Lockdown'

on:
  pull_request_target:
    types: opened

permissions:
  pull-requests: write

jobs:
  action:
    runs-on: ubuntu-latest
    steps:
      - uses: dessant/repo-lockdown@v2
        with:
          pr-comment: |
            Thank you for your interest in the QEMU project.

            This repository is a read-only mirror of the project's repostories hosted
            on https://gitlab.com/qemu-project/qemu.git.
            The project does not process merge requests filed on GitHub.

            QEMU welcomes contributions of code (either fixing bugs or adding new
            functionality). However, we get a lot of patches, and so we have some
            guidelines about contributing on the project website:
            https://www.qemu.org/contribute/
          lock-pr: true
          close-pr: true
