<!--
This is the upstream QEMU issue tracker.

Please note that QEMU, like most open source projects, relies on
contributors who have motivation, skills and available time to work on
implementing particular features.

Feature requests can be helpful for determining demand and interest, but
they are not a guarantee that a contributor will volunteer to implement
it. We welcome and encourage even draft patches to implement a feature
be sent to the mailing list where it can be discussed and developed
further by the community.

Thank you for your interest in helping us to make QEMU better!
-->

## Goal
<!-- Describe the final result you want to achieve. Avoid design specifics. -->


## Technical details
<!-- Describe technical details, design specifics, suggestions, versions, etc. -->


## Additional information
<!-- Patch or branch references, any other useful information -->

<!--
The line below ensures that proper tags are added to the issue.
Please do not remove it.
-->
/label ~"kind::Feature Request"
