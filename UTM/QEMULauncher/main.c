//
// Copyright © 2021 osy. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

#include <stdio.h>
#include "Bootstrap.h"

extern const char **environ;

int main(int argc, const char * argv[]) {
    if (argc < 2) {
        fprintf(stderr, "usage: QEMULauncher dylibPath qemuArguments...\n");
        return 1;
    }
    return startQemuProcess(argv[1], argc - 1, &argv[1], environ);
}
