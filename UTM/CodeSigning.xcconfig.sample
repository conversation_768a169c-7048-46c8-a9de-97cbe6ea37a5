// Your Team ID. See `Documentation/iOSDevelopment.md` for help finding this.
DEVELOPMENT_TEAM = XYZ0123456

// Prefix of unique bundle IDs registered to you in Apple Developer Portal.
// You need to register:
//   - com.myuniquename.UTM
//   - com.myuniquename.QEMUHelper
//   - com.myuniquename.QEMULauncher
PRODUCT_BUNDLE_PREFIX = com.myuniquename

// Set to YES if you have a valid paid Apple Developer account
DEVELOPER_ACCOUNT_PAID = NO

// Set to YES if you have access to VM entitlements in your account
DEVELOPER_ACCOUNT_VM_ACCESS = NO

// Name of the iOS development signing certificate, you probably do not need
// to change this.
CODE_SIGN_IDENTITY_IOS = Apple Development

// The values below are specific to macOS development. If you do not define
// these keys, the build will default to ad-hoc signing. You will need to
// follow `Documentation/MacDevelopment.md` to disable library verification and
// remove unsupported entitlements.

// Name of the macOS development signing certificate. Comment out this line to
// use ad-hoc signing.
CODE_SIGN_IDENTITY_MAC = Apple Development

// Create a Mac provisioning profile for com.myuniquename.UTM with the
// Hypervisor entitlements and get its UUID. If you do not have access to these
// entitlements, comment out the line and delete the following entitlements
//   - com.apple.vm.device-access
// from the following file
//   - Platform/macOS/macOS.entitlements
PROVISIONING_PROFILE_SPECIFIER_MAC = ********-1111-2222-3333-************

// Create a Mac provisioning profile for com.myuniquename.QEMUHelper with the
// Hypervisor entitlements and get its UUID. If you do not have access to these
// entitlements, comment out the line and delete the following entitlements
//   - com.apple.vm.networking
// from the following file
//   - QEMUHelper/QEMUHelper.entitlements
PROVISIONING_PROFILE_SPECIFIER_HELPER = ********-1111-2222-3333-************

// Create a Mac provisioning profile for com.myuniquename.QEMULauncher with the
// Hypervisor entitlements and get its UUID. If you do not have access to these
// entitlements, comment out the line and delete the following entitlements
//   - com.apple.vm.networking
// from the following file
//   - QEMULauncher/QEMULauncher.entitlements
PROVISIONING_PROFILE_SPECIFIER_LAUNCHER = ********-1111-2222-3333-************

// If you are signed in to your developer account on Xcode, leave this as is
// Otherwise, change it to 'Manual' and fill in the profile specifiers below
CODE_SIGN_STYLE_IOS = Automatic

// If using manual iOS signing, fill the profile specifiers for each app
PROVISIONING_PROFILE_SPECIFIER_IOS = 
PROVISIONING_PROFILE_SPECIFIER_SE = 
PROVISIONING_PROFILE_SPECIFIER_REMOTE = 
