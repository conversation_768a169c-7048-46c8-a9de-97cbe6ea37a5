diff -urN a/Configurations/10-main.conf b/Configurations/10-main.conf
--- a/Configurations/10-main.conf	2019-02-26 06:15:30.000000000 -0800
+++ b/Configurations/10-main.conf	2020-11-03 19:48:02.000000000 -0800
@@ -1554,6 +1554,14 @@
         bn_ops           => "SIXTY_FOUR_BIT_LONG",
         perlasm_scheme   => "macosx",
     },
+    "darwin64-arm64-cc" => {
+        inherit_from     => [ "darwin-common", asm("aarch64_asm") ],
+        CFLAGS           => add("-Wall"),
+        cflags           => add("-arch arm64"),
+        lib_cppflags     => add("-DL_ENDIAN"),
+        bn_ops           => "SIXTY_FOUR_BIT_LONG",
+        perlasm_scheme   => "ios64",
+    },
 
 ##### GNU Hurd
     "hurd-x86" => {
diff --color -Naur a/Configurations/15-ios.conf b/Configurations/15-ios.conf
--- a/Configurations/15-ios.conf	2019-02-26 06:15:30
+++ b/Configurations/15-ios.conf	2023-08-07 19:53:43
@@ -32,6 +32,13 @@
         inherit_from     => [ "ios-common" ],
         CC               => "xcrun -sdk iphonesimulator cc",
     },
+    "iossimulator64-xcrun" => {
+        inherit_from     => [ "ios-common", asm("x86_64_asm") ],
+        CC               => "xcrun -sdk iphonesimulator cc",
+        cflags           => add("-arch x86_64 -mios-version-min=7.0.0 -fno-common"),
+        bn_ops           => "SIXTY_FOUR_BIT_LONG RC4_CHAR",
+        perlasm_scheme   => "macosx",
+    },
 # It takes three prior-set environment variables to make it work:
 #
 # CROSS_COMPILE=/where/toolchain/is/usr/bin/ [note ending slash]
@@ -58,5 +65,35 @@
         inherit_from     => [ "ios64-xcrun" ],
         CC               => "cc",
         cflags           => add("-isysroot \$(CROSS_TOP)/SDKs/\$(CROSS_SDK)"),
+    },
+    "iossimulator64-cross" => {
+        inherit_from     => [ "iossimulator64-xcrun" ],
+        CC               => "cc",
+        cflags           => add("-isysroot \$(CROSS_TOP)/SDKs/\$(CROSS_SDK)"),
+    },
+## Apple visionOS
+    "visionos-common" => {
+        template         => 1,
+        inherit_from     => [ "darwin-common" ],
+        sys_id           => "visionOS",
+        disable          => [ "engine", "async" ],
+    },
+    "visionos-cross-arm64" => {
+        inherit_from     => [ "visionos-common", asm("aarch64_asm") ],
+        CC               => "xcrun -sdk xros cc",
+        cflags           => add("-arch arm64"),
+        bn_ops           => "SIXTY_FOUR_BIT_LONG RC4_CHAR",
+        perlasm_scheme   => "ios64",
+    },
+    "visionos-sim-cross-arm64" => {
+        inherit_from     => [ "visionos-common" ],
+        CC               => "xcrun -sdk xrsimulator cc",
+    },
+    "visionos-sim-cross-x86_64" => {
+        inherit_from     => [ "visionos-common", asm("x86_64_asm") ],
+        CC               => "xcrun -sdk xrsimulator cc",
+        cflags           => add("-arch x86_64"),
+        bn_ops           => "SIXTY_FOUR_BIT_LONG RC4_CHAR",
+        perlasm_scheme   => "macosx",
     },
 );
