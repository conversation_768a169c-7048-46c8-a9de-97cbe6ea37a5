From b5a7dca8cba163ca15b873c3ac9c81ec5622e827 Mon Sep 17 00:00:00 2001
From: osy <<EMAIL>>
Date: Sat, 15 May 2021 23:29:54 -0700
Subject: [PATCH] darwin: reset by re-enumerate on non-macOS platforms

On non-macOS platforms, ResetDevice() does nothing so we use the "old" way of
calling re-enumerate. If the device is captured, we have to re-enumerate, then
re-capture, re-authorize, and finally restore the state.
---
 libusb/os/darwin_usb.c | 28 ++++++++++++++++++++++++++--
 1 file changed, 26 insertions(+), 2 deletions(-)

diff --git a/libusb/os/darwin_usb.c b/libusb/os/darwin_usb.c
index 903422c..c2a4813 100644
--- a/libusb/os/darwin_usb.c
+++ b/libusb/os/darwin_usb.c
@@ -95,6 +95,8 @@ static enum libusb_error process_new_device (struct libusb_context *ctx, struct
 static enum libusb_error darwin_get_cached_device(struct libusb_context *ctx, io_service_t service, struct darwin_cached_device **cached_out,
                                                   UInt64 *old_session_id);
 
+static int darwin_detach_kernel_driver (struct libusb_device_handle *dev_handle, uint8_t interface);
+
 #if defined(ENABLE_LOGGING)
 static const char *darwin_error_str (IOReturn result) {
   static char string_buffer[50];
@@ -1842,15 +1844,37 @@ static int darwin_reenumerate_device (struct libusb_device_handle *dev_handle, b
 
 static int darwin_reset_device (struct libusb_device_handle *dev_handle) {
   struct darwin_cached_device *dpriv = DARWIN_CACHED_DEVICE(dev_handle->dev);
+  unsigned long claimed_interfaces = dev_handle->claimed_interfaces;
+  int8_t active_config = dpriv->active_config;
+  int capture_count;
   IOReturn kresult;
+  enum libusb_error ret;
 
+#if TARGET_OS_OSX
+  /* ResetDevice() is missing on non-macOS platforms */
   if (dpriv->capture_count > 0) {
     /* we have to use ResetDevice as USBDeviceReEnumerate() loses the authorization for capture */
     kresult = (*(dpriv->device))->ResetDevice (dpriv->device);
     return darwin_to_libusb (kresult);
-  } else {
-    return darwin_reenumerate_device (dev_handle, false);
   }
+#endif
+  ret = darwin_reenumerate_device (dev_handle, false);
+  if ((ret == LIBUSB_SUCCESS || ret == LIBUSB_ERROR_NOT_FOUND) && dpriv->capture_count > 0) {
+    /* save old capture_count */
+    capture_count = dpriv->capture_count;
+    /* reset capture count */
+    dpriv->capture_count = 0;
+    /* attempt to detach kernel driver again as it is now re-attached */
+    ret = darwin_detach_kernel_driver (dev_handle, 0);
+    if (ret != LIBUSB_SUCCESS) {
+      return ret;
+    }
+    /* restore capture_count */
+    dpriv->capture_count = capture_count;
+    /* restore configuration */
+    ret = darwin_restore_state (dev_handle, active_config, claimed_interfaces);
+  }
+  return ret;
 }
 
 static io_service_t usb_find_interface_matching_location (const io_name_t class_name, UInt8 interface_number, UInt32 location) {
-- 
2.32.0 (Apple Git-132)

