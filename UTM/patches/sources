#!/bin/sh

# pkg-config
PKG_CONFIG_SRC="https://pkgconfig.freedesktop.org/releases/pkg-config-0.29.2.tar.gz"

# Source files for qemu
FFI_SRC="https://github.com/libffi/libffi/releases/download/v3.5.0/libffi-3.5.0.tar.gz"
ICONV_SRC="https://ftp.gnu.org/gnu/libiconv/libiconv-1.16.tar.gz"
GETTEXT_SRC="https://ftp.gnu.org/gnu/gettext/gettext-0.22.5.tar.gz"
PNG_SRC="https://ftp.osuosl.org/pub/blfs/conglomeration/libpng/libpng-1.6.48.tar.xz"
JPEG_TURBO_SRC="https://ftp.osuosl.org/pub/blfs/conglomeration/libjpeg-turbo/libjpeg-turbo-1.5.3.tar.gz"
GLIB_SRC="https://download.gnome.org/sources/glib/2.83/glib-2.83.0.tar.xz"
GPG_ERROR_SRC="https://www.gnupg.org/ftp/gcrypt/libgpg-error/libgpg-error-1.38.tar.gz"
GCRYPT_SRC="https://www.gnupg.org/ftp/gcrypt/libgcrypt/libgcrypt-1.8.4.tar.gz"
PIXMAN_SRC="https://www.cairographics.org/releases/pixman-0.38.0.tar.gz"
OPENSSL_SRC="https://www.openssl.org/source/old/1.1.1/openssl-1.1.1b.tar.gz"
TPMS_SRC="https://github.com/osy/libtpms/releases/download/v0.9.6/libtpms-0.9.6.tar.gz"
SWTPM_SRC="https://github.com/utmapp/swtpm/releases/download/v0.8.99/swtpm-0.8.99.tar.gz"
OPUS_SRC="https://archive.mozilla.org/pub/opus/opus-1.3.tar.gz"
ZSTD_SRC="https://github.com/facebook/zstd/releases/download/v1.5.2/zstd-1.5.2.tar.gz"
SPICE_PROTOCOL_SRC="https://www.spice-space.org/download/releases/spice-protocol-0.14.4.tar.xz"
SPICE_SERVER_SRC="https://www.spice-space.org/download/releases/spice-server/spice-0.14.3.tar.bz2"
USB_SRC="https://github.com/libusb/libusb/releases/download/v1.0.25/libusb-1.0.25.tar.bz2"
USBREDIR_SRC="https://www.spice-space.org/download/usbredir/usbredir-0.14.0.tar.xz"
SLIRP_SRC="https://gitlab.freedesktop.org/slirp/libslirp/-/archive/v4.9.1/libslirp-v4.9.1.tar.gz"
QEMU_SRC="https://github.com/utmapp/qemu/releases/download/v10.0.2-utm/qemu-10.0.2-utm.tar.xz"

# Source files for spice-client
JSON_GLIB_SRC="https://download.gnome.org/sources/json-glib/1.10/json-glib-1.10.0.tar.xz"
GST_SRC="https://gstreamer.freedesktop.org/src/gstreamer/gstreamer-1.19.1.tar.xz"
GST_BASE_SRC="https://gstreamer.freedesktop.org/src/gst-plugins-base/gst-plugins-base-1.19.1.tar.xz"
GST_GOOD_SRC="https://gstreamer.freedesktop.org/src/gst-plugins-good/gst-plugins-good-1.19.1.tar.xz"
XML2_SRC="http://xmlsoft.org/sources/libxml2-2.9.12.tar.gz"
SOUP_SRC="https://download.gnome.org/sources/libsoup/3.6/libsoup-3.6.0.tar.xz"
PHODAV_SRC="https://download.gnome.org/sources/phodav/3.0/phodav-3.0.tar.xz"
SPICE_CLIENT_SRC="https://www.spice-space.org/download/gtk/spice-gtk-0.42.tar.xz"
LIBUCONTEXT_REPO="https://github.com/utmapp/libucontext.git"
LIBUCONTEXT_COMMIT="9b1d8f01a6e99166f9808c79966abe10786de8b6"

# Source files for GPU acceleration
WEBKIT_REPO="https://github.com/utmapp/WebKit.git"
WEBKIT_COMMIT="b5f22a32a49682059749b2cccac06231a20c1387"
WEBKIT_SUBDIRS="Source/ThirdParty/ANGLE Configurations Tools/ccache"
EPOXY_REPO="https://github.com/utmapp/libepoxy.git"
EPOXY_COMMIT="266d2290a437c655f7419e85af06bfbb73a720c4"
VIRGLRENDERER_REPO="https://github.com/utmapp/virglrenderer.git"
VIRGLRENDERER_COMMIT="dc039d9ecd74fc671a85bfbe7c4e4bc552b7b855"

# Decompiled Hypervisor for iOS
HYPERVISOR_REPO="https://github.com/utmapp/Hypervisor.git"
HYPERVISOR_COMMIT="f8fc8d8f50c23d348ece1152b378ed020da1eb86"
