
/** UTM **/

/* Configuration */

// Legacy/UTMLegacyQemuConfiguration+Constants.m
"Hard Disk" = "ハードディスク";
"CD/DVD" = "CD/DVD";
"Floppy" = "フロッピー";
"None" = "なし";
"Disk Image" = "ディスクイメージ";
"CD/DVD (ISO) Image" = "CD/DVD（ISO）イメージ";
"BIOS" = "BIOS";
"Linux Kernel" = "Linuxカーネル";
"Linux RAM Disk" = "Linux RAMディスク";
"Linux Device Tree Binary" = "Linuxデバイスツリーバイナリ";

// UTMConfiguration.swift
"This configuration is too old and is not supported." = "この構成は古すぎるため、対応していません。";
"This configuration is saved with a newer version of UTM and is not compatible with this version." = "この構成は新しいバージョンのUTMで保存されており、このバージョンとは互換性がありません。";
"An invalid value of '%@' is used in the configuration file." = "構成ファイルで“%@”の無効な値が使用されています。";
"The backend for this configuration is not supported." = "この構成のバックエンドには対応していません。";
"The drive '%@' already exists and cannot be created." = "ドライブ“%@”がすでに存在するため、作成できません。";
"An internal error has occurred." = "内部エラーが発生しました。";

// UTMConfigurationInfo.swift
"Virtual Machine" = "仮想マシン";

// UTMAppleConfiguration.swift
"This is not a valid Apple Virtualization configuration." = "有効なApple仮想化構成ではありません。";
"This virtual machine cannot run on the current host machine." = "この仮想マシンは現在のホストマシンでは実行できません。";
"A valid kernel image must be specified." = "有効なカーネルイメージを指定する必要があります。";
"This virtual machine contains an invalid hardware model. The configuration may be corrupted or is outdated." = "この仮想マシンには無効なハードウェアモデルが含まれています。構成が破損しているか、古くなっている可能性があります。";
"Rosetta is not supported on the current host machine." = "Rosettaは現在のホストマシンでは対応していません。";
"The host operating system needs to be updated to support one or more features requested by the guest." = "ゲストから要求された1つ以上の機能に対応するには、ホストのオペレーティングシステムをアップデートする必要があります。";

// UTMAppleConfigurationBoot.swift
"Linux" = "Linux";
"macOS" = "macOS";

// UTMAppleConfigurationNetwork.swift
"Shared Network" = "共有ネットワーク";
"Bridged (Advanced)" = "ブリッジ（詳細）";

// UTMAppleConfigurationSerial.swift
"Built-in Terminal" = "内蔵ターミナル";
"Pseudo-TTY Device" = "疑似TTYデバイス";

// UTMAppleConfigurationVirtualization.swift
"Disabled" = "無効";
"Generic Mouse" = "汎用マウス";
"Mac Trackpad (macOS 13+)" = "Macトラックパッド（macOS 13以降）";
"Generic USB" = "汎用USB";
"Mac Keyboard (macOS 14+)" = "Macキーボード（macOS 14以降）";

// UTMQemuConfiguration.swift
"Failed to migrate configuration from a previous UTM version." = "以前のUTMバージョンからの構成の移行に失敗しました。";
"UEFI is not supported with this architecture." = "UEFIはこのアーキテクチャには対応していません。";

// QEMUConstant.swift
"Linear" = "リニア";
"Nearest Neighbor" = "直近ピクセル";
"USB 2.0" = "USB 2.0";
"USB 3.0 (XHCI)" = "USB 3.0（XHCI）";
"Emulated VLAN" = "仮想VLAN";
"Host Only" = "ホストのみ";
"TCP" = "TCP";
"UDP" = "UDP";
"Default" = "デフォルト";
"Italic, Bold" = "イタリック、ボールド";
"Italic" = "イタリック";
"Bold" = "ボールド";
"Regular" = "レギュラー";
"%@ (%@)" = "%1$@（%2$@）";
"TCP Client Connection" = "TCPクライアント接続";
"TCP Server Connection" = "TCPサーバ接続";
"Automatic Serial Device (max 4)" = "自動シリアルデバイス（最大4台）";
"Manual Serial Device (advanced)" = "手動シリアルデバイス（詳細）";
"GDB Debug Stub" = "GDBデバッグスタブ";
"QEMU Monitor (HMP)" = "QEMUモニタ（HMP）";
"None (Advanced)" = "なし（詳細）";
"IDE" = "IDE";
"SCSI" = "SCSI";
"SD Card" = "SDカード";
"MTD (NAND/NOR)" = "MTD（NAND/NOR）";
"Floppy" = "フロッピー";
"PC System Flash" = "PCシステムフラッシュ";
"VirtIO" = "VirtIO";
"NVMe" = "NVMe";
"USB" = "USB";
"SPICE WebDAV" = "SPICE WebDAV";
"VirtFS" = "VirtFS";


/* Services */

// UTMPipeInterface.swift
"Failed to create pipe for communications." = "通信用パイプの作成に失敗しました。";

// UTMProcess.m
"Internal error has occurred." = "内部エラーが発生しました。";

// UTMQemuImage.swift
"An unknown QEMU error has occurred." = "不明なQEMUエラーが発生しました。";

// UTMSpiceIO.m
"Failed to change current directory." = "作業ディレクトリの変更に失敗しました。";
"Failed to start SPICE client." = "SPICEクライアントの開始に失敗しました。";
"Internal error trying to connect to SPICE server." = "SPICEサーバへの接続試行中に内部エラーが発生しました。";

// UTMVirtualMachine.swift
"Not implemented." = "実装されていません。";

// UTMAppleVirtualMachine.swift
"Cannot create virtual terminal." = "仮想ターミナルを作成できません。";
"Cannot access resource: %@" = "リソースにアクセスできません: %@";
"The operating system cannot be installed on this machine." = "そのオペレーティングシステムはこのマシン上ではインストールできません。";
"The operation is not available." = "その操作は利用できません。";

// UTMQemuVirtualMachine.swift
"Suspend state cannot be saved when running in disposible mode." = "使い捨てモードで動作している場合、サスペンド状態を保存することはできません。";
"Suspend is not supported for virtualization." = "サスペンドは仮想化には対応していません。";
"Suspend is not supported when GPU acceleration is enabled." = "サスペンドはGPUアクセラレーションが有効な場合には対応していません。";
"Suspend is not supported when an emulated NVMe device is active." = "サスペンドは仮想NVMeデバイスがアクティブな場合には対応していません。";
"Failed to access data from shortcut." = "ショートカットからデータのアクセスに失敗しました。";
"This build of UTM does not support emulating the architecture of this VM." = "このビルドのUTMは、この仮想マシンのアーキテクチャのエミュレーションには対応していません。";
"Failed to access drive image path." = "ドライブイメージパスのアクセスに失敗しました。";
"Failed to access shared directory." = "共有ディレクトリのアクセスに失敗しました。";
"The virtual machine is in an invalid state." = "仮想マシンが無効な状態です。";
"Failed to save VM snapshot. Usually this means at least one device does not support snapshots. %@" = "仮想マシンのスナップショットの保存に失敗しました。これは、通常1台以上のデバイスがスナップショットに対応していないことを意味します。%@";
"Failed to generate TLS key for server." = "サーバ用のTLS鍵の生成に失敗しました。";


/* Platform/iOS */

// UTMDataExtension.swift
"This virtual machine is already running. In order to run it from this device, you must stop it first." = "この仮想マシンはすでに実行されています。このデバイスから実行するには、まず停止する必要があります。";

// UTMDonateView.swift
"Your support is the driving force that helps UTM stay independent. Your contribution, no matter the size, makes a significant difference. It enables us to develop new features and maintain existing ones. Thank you for considering a donation to support us." = "皆様のご支援がUTMの独立を維持する原動力となります。皆様からのご支援は、その大小にかかわらず、大きな違いをもたらします。これにより、新しい機能を開発し、既存の機能を維持することができます。ぜひご寄付をご検討のほどよろしくお願いいたします。";
"GitHub Sponsors" = "GitHub Sponsors";
"Support UTM" = "UTMを支援";
"One Time Donation" = "1回限りの寄付";
"Recurring Donation" = "定期寄付";
"Manage Subscriptions…" = "サブスクリプションを管理…";
"Restore Purchases" = "購入を復元";
"%d days" = "%d日間";
"day" = "1日間";
"%d weeks" = "%d週間";
"week" = "1週間";
"%d months" = "%dか月間";
"month" = "1か月間";
"%d years" = "%d年間";
"year" = "1年間";
"period" = "期間";
"Your purchase could not be verified by the App Store." = "App Storeで購入が確認できませんでした。";

// UTMSingleWindowView.swift
"Waiting for VM to connect to display..." = "仮想マシンがディスプレイに接続するのを待機中…";

// UTMRemoteConnectView.swift
"Select a UTM Server" = "UTMサーバを選択してください";
"Help" = "ヘルプ";
"New Connection" = "新規接続";
"Saved" = "保存済み";
"Edit…" = "編集…";
"Delete" = "削除";
"Discovered" = "検出";
"Make sure the latest version of UTM is running on your Mac and UTM Server is enabled. You can download UTM from the Mac App Store." = "最新バージョンのUTMがMac上で実行されており、UTMサーバが有効になっていることを確認してください。UTMはMac App Storeからダウンロードできます。";
"Name (optional)" = "名前（オプション）";
"Hostname or IP address" = "ホスト名またはIPアドレス";
"Port" = "ポート";
"Host" = "ホスト";
"Fingerprint" = "指紋";
"Password" = "パスワード";
"Save Password" = "パスワードを保存";
"Close" = "閉じる";
"Cancel" = "キャンセル";
"Trust" = "信頼";
"Connect" = "接続";
"Timed out trying to connect." = "接続試行中にタイムアウトになりました。";

// UTMSettingsView.swift
"Settings" = "設定";

// VMConfigNetworkPortForwardView.swift
"Port Forward" = "ポート転送";
"%@ ➡️ %@" = "%1$@ ➡️ %2$@";
"New" = "新規";
"Save" = "保存";

// VMDrivesSettingsView.swift
"Confirm Delete" = "削除の確認";
"Are you sure you want to permanently delete this disk image?" = "このディスクイメージを完全に削除してもよろしいですか?";
"EFI Variables" = "EFI変数";
"%@ Drive" = "%@ドライブ";
"Done" = "完了";

// VMSettingsView.swift
"Information" = "情報";
"System" = "システム";
"QEMU" = "QEMU";
"Input" = "入力";
"Sharing" = "共有";
"Show all devices…" = "すべてのデバイスを表示…";
"Devices" = "デバイス";
"Display" = "ディスプレイ";
"Serial" = "シリアル";
"Network" = "ネットワーク";
"Sound" = "サウンド";
"Drives" = "ドライブ";
"Version" = "バージョン";
"Build" = "ビルド";

// VMToolbarView.swift
"Power Off" = "電源オフ";
"Force Kill" = "強制終了";
"Pause" = "一時停止";
"Play" = "再生";
"Restart" = "再起動";
"Zoom" = "ズーム";
"Keyboard" = "キーボード";
"Hide" = "非表示";

// VMToolbarDisplayMenuView.swift
"Serial %lld: %@" = "シリアル%1$lld: %2$@";
"Current Window" = "現在のウインドウ";
"Zoom/Reset" = "ズーム/リセット";
"External Monitor" = "外部モニタ";
"New Window…" = "新規ウインドウ…";

// VMToolbarDriveMenuView.swift
"none" = "なし";
"Change…" = "変更…";
"Clear…" = "消去…";
"Shared Directory: %@" = "共有ディレクトリ: %@";
"Eject…" = "取り出す…";
"Disk" = "ディスク";
"%@ (%@): %@" = "%1$@（%2$@）: %3$@";

// VMToolbarUSBMenuView.swift
"No USB devices detected." = "USBデバイスが検出されませんでした。";

// VMWindowView.swift
"Resume" = "再開";
"Are you sure you want to stop this VM and exit? Any unsaved changes will be lost." = "この仮想マシンを停止し、終了してもよろしいですか? 保存されていない変更内容は失われます。";
"No" = "いいえ";
"Yes" = "はい";
"Are you sure you want to exit UTM?" = "UTMを終了してもよろしいですか?";
"Are you sure you want to reset this VM? Any unsaved changes will be lost." = "この仮想マシンをリセットしてもよろしいですか? 保存されていない変更内容は失われます。";
"Would you like to connect '%@' to this virtual machine?" = "この仮想マシンに“%@”を接続しますか?";
"OK" = "OK";
"Running low on memory! UTM might soon be killed by iOS. You can prevent this by decreasing the amount of memory and/or JIT cache assigned to this VM" = "メモリが不足しています! UTMはまもなくiOSによって強制終了される可能性があります。この仮想マシンに割り当てられているメモリやJITキャッシュの量を減らすことで防ぐことができます";
"No output device is selected for this window." = "このウインドウでは出力デバイスが選択されていません。";

// VMWizardView.swift
"Continue" = "続ける";


/* Platform/macOS */

// Display/VMDisplayWindowController.swift
"This may corrupt the VM and any unsaved changes will be lost. To quit safely, shut down from the guest." = "この操作により、仮想マシンを破損させる可能性があり、保存されていない変更内容が失われます。安全に終了するには、ゲストからシステム終了してください。";
"This will reset the VM and any unsaved state will be lost." = "この操作により、仮想マシンがリセットされ、保存されていない状態が失われます。";
"Error" = "エラー";
"Confirmation" = "確認";
"Failed to save suspend state" = "サスペンド状態の保存に失敗しました";
"Closing this window will kill the VM." = "このウインドウを閉じると、仮想マシンを強制終了します。";
"Request power down" = "電源オフを要求";
"Sends power down request to the guest. This simulates pressing the power button on a PC." = "ゲストに電源オフ要求を送信します。これにより、PCの電源ボタン押下をシミュレートします。";
"Force shut down" = "強制システム終了";
"Tells the VM process to shut down with risk of data corruption. This simulates holding down the power button on a PC." = "仮想マシンのプロセスにデータ破損のリスクを伴うシステム終了を指示します。これにより、PCの電源ボタン長押しをシミュレートします。";
"Force kill" = "強制終了";
"Force kill the VM process with high risk of data corruption." = "仮想マシンのプロセスを強制終了します。高いデータ破損のリスクを伴います。";

// Display/VMDisplayAppleWindowController.swift
"Would you like to install macOS? If an existing operating system is already installed on the primary drive of this VM, then it will be erased." = "macOSをインストールしますか? この仮想マシンのプライマリドライブに、すでにオペレーティングシステムがインストールされている場合は消去されます。";
"Directory sharing" = "ディレクトリ共有";
"To access the shared directory, the guest OS must have Virtiofs drivers installed. You can then run `sudo mount -t virtiofs share /path/to/share` to mount to the share path." = "共有ディレクトリにアクセスするには、ゲストOSにVirtioFSドライバがインストールされている必要があります。`sudo mount -t virtiofs share /path/to/share`を実行して共有パスにマウントできます。";
"Read Only" = "読み出しのみ";
"Remove…" = "削除…";
"Add…" = "追加…";
"Select Shared Folder" = "共有フォルダを選択してください";
"Installation: %@" = "インストール: %@";
"Serial %lld" = "シリアル%lld";

// Display/VMDisplayAppleDisplayWindowController.swift
"%@ (Terminal %lld)" = "%1$@（ターミナル%2$lld）";
"Querying drives status..." = "ドライブの状態を確認中…";
"No drives connected." = "デバイスが接続されていません。";
"Install Guest Tools…" = "ゲストツールをインストール…";
"Eject" = "取り出す";
"Change" = "変更";
"Select Drive Image" = "ドライブイメージを選択してください";
"USB Mass Storage: %@" = "USB Mass Storage: %@";
"An USB device containing the installer will be mounted in the virtual machine. Only macOS Sequoia (15.0) and newer guests are supported." = "インストーラを含むUSBデバイスが仮想マシンにマウントされます。macOS Sequoia（15.0）以降のゲストにのみ対応しています。";

// Display/VMDisplayQemuDisplayController.swift
"Disposable Mode" = "使い捨てモード";
"Install Windows Guest Tools…" = "Windowsゲストツールをインストール…";
"USB Device" = "USBデバイス";
"Confirm" = "確定";
"Querying USB devices..." = "USBデバイスを確認中…";
"Serial %lld: %@" = "シリアル%1$lld: %2$@";
"Display %lld: %@" = "ディスプレイ%1$lld: %2$@";

// Display/VMDisplayQemuMetalWindowController.swift
"%@ (Display %lld)" = "%1$@（ディスプレイ%2$lld）";
"Metal is not supported on this device. Cannot render display." = "Metalはこのデバイスでは対応していないため、ディスプレイを描画できません。";
"Internal error." = "内部エラーが発生しました。";
"Press %@ to release cursor" = "%@を押してカーソルを解放します";
"⌘+⌥" = "⌘＋⌥";
"⌃+⌥" = "⌃＋⌥";
"Captured mouse" = "マウスがキャプチャされました";
"To release the mouse cursor, press %@ at the same time." = "マウスカーソルを解放するには、%@を同時に押します。";
"⌘+⌥ (Cmd+Opt)" = "⌘＋⌥（Command＋Option）";
"⌃+⌥ (Ctrl+Opt)" = "⌃＋⌥（Control＋Option）";

// Display/VMMetalView.swift
"Capture Input" = "入力をキャプチャ";
"To capture input or to release the capture, press Command and Option at the same time." = "入力をキャプチャまたはキャプチャを解除するには、コマンドとオプションを同時に押します。";

// AppDelegate.swift
"Quitting UTM will kill all running VMs." = "UTMを終了すると、すべての仮想マシンが強制終了します。";

// SettingsView.swift
"Application" = "アプリケーション";
"Keep UTM running after last window is closed and all VMs are shut down" = "最後のウインドウが閉じられ、すべての仮想マシンがシステム終了した後もUTMを実行し続ける";
"Show dock icon" = "Dockアイコンを表示";
"Show menu bar icon" = "メニューバーアイコンを表示";
"Prevent system from sleeping when any VM is running" = "仮想マシン実行中にシステムがスリープしないようにする";
"Do not show confirmation when closing a running VM" = "実行中の仮想マシンを閉じるときに確認を表示しない";
"Closing a VM without properly shutting it down could result in data loss." = "仮想マシンを適切にシステム終了せずに閉じると、データが失われる可能性があります。";
"Do not save VM screenshot to disk" = "仮想マシンのスクリーンショットをディスクに保存しない";
"If enabled, any existing screenshot will be deleted the next time the VM is started." = "有効にすると、次回仮想マシンを開始したときに既存のスクリーンショットが削除されます。";
"QEMU Graphics Acceleration" = "QEMUグラフィックスアクセラレーション";
"Renderer Backend" = "レンダラバックエンド";
"ANGLE (OpenGL)" = "ANGLE（OpenGL）";
"ANGLE (Metal)" = "ANGLE（Metal）";
"By default, the best renderer for this device will be used. You can override this with to always use a specific renderer. This only applies to QEMU VMs with GPU accelerated graphics." = "デフォルトでは、このデバイスに最適なレンダラが使用されます。これをオーバーライドして常に特定のレンダラを使用することもできます。GPUグラフィックスアクセラレーションが有効なQEMU仮想マシンにのみ適用されます。";
"FPS Limit" = "FPS制限";
"If set, a frame limit can improve smoothness in rendering by preventing stutters when set to the lowest value your device can handle." = "フレーム制限を設定すると、お使いのデバイスが処理できる最小値に設定したときに画面のカクつきを防止し、レンダリングの滑らかさを向上させることができます。";
"QEMU Sound" = "QEMUサウンド";
"Sound Backend" = "サウンドバックエンド";
"SPICE with GStreamer (Input & Output)" = "GStreamerを使用したSPICE（入出力）";
"CoreAudio (Output Only)" = "CoreAudio（出力のみ）";
"Mouse/Keyboard" = "マウス/キーボード";
"Capture input automatically when entering full screen" = "フルスクリーンになったときに入力を自動的にキャプチャ";
"If enabled, input capture will toggle automatically when entering and exiting full screen mode." = "有効にすると、フルスクリーンモードの開始時と解除時に入力キャプチャが自動的に切り替わります。";
"Capture input automatically when window is focused" = "ウインドウがフォーカスされたときに入力を自動的にキャプチャ";
"If enabled, input capture will toggle automatically when the VM's window is focused." = "有効にすると、仮想マシンのウインドウがフォーカスされたときに入力キャプチャが自動的に切り替わります。";
"Console" = "コンソール";
"Option (⌥) is Meta key" = "メタキーとしてOption（⌥）を使用";
"If enabled, Option will be mapped to the Meta key which can be useful for emacs. Otherwise, option will work as the system intended (such as for entering international text)." = "有効にすると、OptionがEmacsの利用に便利なメタキーにマッピングされます。そうでない場合、Optionはシステムが意図したとおりに動作します（国際テキストを入力する場合など）。";
"QEMU Pointer" = "QEMUポインタ";
"Hold Control (⌃) for right click" = "Control（⌃）を押している間は右クリックにする";
"Invert scrolling" = "スクロールを反転";
"If enabled, scroll wheel input will be inverted." = "有効にすると、スクロールホイールの入力が反転します。";
"QEMU Keyboard" = "QEMUキーボード";
"Use Command+Option (⌘+⌥) for input capture/release" = "入力のキャプチャ/解放にCommand＋Option（⌘＋⌥）を使用";
"If disabled, the default combination Control+Option (⌃+⌥) will be used." = "無効にすると、デフォルトのコンビネーションControl＋Option（⌃＋⌥）が使用されます。";
"Caps Lock (⇪) is treated as a key" = "Caps Lock（⇪）をキーとして扱う";
"If enabled, caps lock will be handled like other keys. If disabled, it is treated as a toggle that is synchronized with the host." = "有効にすると、Caps Lockがほかのキーと同様に処理されます。無効にすると、ホストと同期するトグルとして扱われます。";
"Num Lock is forced on" = "NumLockを強制的にオンにする";
"If enabled, num lock will always be on to the guest. Note this may make your keyboard's num lock indicator out of sync." = "有効にすると、ゲストに対してNumLockが常にオンになります。これにより、キーボードのNumLockインジケータが同期しなくなる可能性があることに注意してください。";
"QEMU USB" = "QEMU USB";
"Do not show prompt when USB device is plugged in" = "USBデバイス挿入時にプロンプトを表示しない";
"Startup" = "起動時";
"Automatically start UTM server" = "UTMサーバを自動的に起動";
"Reject unknown connections by default" = "デフォルトで不明な接続を拒否";
"If checked, you will not be prompted about any unknown connection and they will be rejected." = "チェックを入れると、不明な接続についてのプロンプトは表示されず拒否されます。";
"Allow access from external clients" = "外部クライアントからのアクセスを許可";
"By default, the server is only available on LAN but setting this will use UPnP/NAT-PMP to port forward to WAN." = "デフォルトでは、サーバはLAN上でのみ利用可能ですが、これを設定すると、UPnP/NAT-PMPを使用してWANにポート転送します。";
"Any" = "任意";
"Specify a port number to listen on. This is required if external clients are permitted." = "外部からの接続を受け入れるポート番号を指定します。これは、外部クライアントが許可されている場合に必要です。";
"Authentication" = "認証";
"Require Password" = "パスワードが必要";
"If enabled, clients must enter a password. This is required if you want to access the server externally." = "有効にすると、クライアントはパスワードを入力する必要があります。これは、サーバに外部からアクセスする場合に必要です。";

// UTMApp.swift
"UTM" = "UTM";
"UTM Server" = "UTMサーバ";

// UTMDataExtension.swift
"This virtual machine cannot be run on this machine." = "この仮想マシンはこのマシンでは実行できません。";

// UTMMenuBarExtraScene.swift
"Show UTM" = "UTMを表示";
"Show the main window." = "メインウインドウを表示します。";
"Hide dock icon on next launch" = "次回起動時にDockアイコンを非表示にする";
"Requires restarting UTM to take affect." = "変更を適用するには、UTMを再起動する必要があります。";
"No virtual machines found." = "仮想マシンが見つかりません。";
"Quit" = "終了";
"Terminate UTM and stop all running VMs." = "UTMを終了し、すべての実行中の仮想マシンを停止します。";
"Start" = "開始";
"Stop" = "停止";
"Suspend" = "サスペンド";
"Reset" = "リセット";
"Busy…" = "処理中…";

// UTMServer.swift
"Enable UTM Server" = "UTMサーバを有効にする";
"Reset Identity" = "IDをリセット";
"Do you want to forget all clients and generate a new server identity? Any clients that previously paired with this server will be instructed to manually unpair with this server before they can connect again." = "すべてのクライアントを削除して、新しいサーバIDを生成しますか? 以前このサーバとペアリングしていたクライアントは、再度接続する前に手動でこのサーバとのペアリングを解除するよう指示されます。";
"Server IP: %s, Port: %s" = "サーバIP: %1$s、ポート: %2$s";
"Running" = "動作中";
"Name" = "名前";
"Last Seen" = "最終接続";
"Status" = "状態";
"Connected" = "接続済み";
"Blocked" = "ブロック済み";
"Approve" = "承認";
"Block" = "ブロック";
"Disconnect" = "切断";
"Do you want to forget the selected client(s)?" = "選択中のクライアントを削除しますか?";

// VMConfigAppleBootView.swift
"Operating System" = "オペレーティングシステム";
"Bootloader" = "ブートローダ";
"UEFI" = "UEFI";
"Please select an uncompressed Linux kernel image." = "無圧縮Linuxカーネルイメージを選択してください。";
"Please select a macOS recovery IPSW." = "macOSの復元IPSWを選択してください。";
"This operating system is unsupported on your machine." = "このオペレーティングシステムはお使いのマシンでは対応していません。";
"Select a file." = "ファイルを選択してください。";
"Linux Settings" = "Linux設定";
"Kernel Image" = "カーネルイメージ";
"Ramdisk (optional)" = "RAMディスク（オプション）";
"Clear" = "消去";
"Boot Arguments" = "起動引数";
"macOS Settings" = "macOS設定";
"IPSW Install Image" = "IPSWインストールイメージ";
"Your machine does not support running this IPSW." = "お使いのマシンはこのIPSWの実行に対応していません。";

// VMConfigAppleDisplayView.swift
"Custom" = "カスタム";
"Resolution" = "解像度";
"Width" = "幅";
"Height" = "高さ";
"HiDPI (Retina)" = "高DPI（Retina）";
"Only available on macOS virtual machines." = "macOS仮想マシンでのみ使用できます。";
"Dynamic Resolution" = "ダイナミック解像度";
"Only available on macOS 14+ virtual machines." = "macOS 14以降の仮想マシンでのみ使用できます。";

// VMConfigAppleDriveCreateView.swift
"Removable" = "リムーバブル";
"If checked, the drive image will be stored with the VM." = "チェックを入れると、ドライブイメージが仮想マシンに保存されます。";
"Use NVMe Interface" = "NVMeインターフェイスを使用";
"If checked, use NVMe instead of virtio as the disk interface, available on macOS 14+ for Linux guests only. This interface is slower but less likely to encounter filesystem errors." = "チェックを入れると、Virtioの代わりにNVMeをディスクインターフェイスとして使用します。これは、macOS 14以降のLinuxゲストでのみ使用できます。このインターフェイスは低速ですが、ファイルシステムエラーが発生する可能性が低くなります。";

// VMConfigAppleDriveDetailsView.swift
"Removable Drive" = "リムーバブルドライブ";
"(New Drive)" = "（新規ドライブ）";
"Read Only?" = "読み出しのみ";
"Delete Drive" = "ドライブを削除";
"Delete this drive." = "このドライブを削除します。";

// VMConfigAppleNetworkingView.swift
"Network Mode" = "ネットワークモード";
"MAC Address" = "MACアドレス";
"Random" = "ランダム";
"Bridged Settings" = "ブリッジ設定";
"Interface" = "インターフェイス";
"Automatic" = "自動";
"Invalid MAC address." = "MACアドレスが無効です。";

// VMConfigAppleSerialView.swift
"Connection" = "接続";
"Mode" = "モード";

// VMConfigAppleSharingView.swift
"Shared directories in macOS VMs are only available in macOS 13 and later." = "macOS仮想マシンの共有ディレクトリはmacOS 13以降でのみ使用できます。";
"Shared Path" = "共有パス";
"Add" = "追加";
"This directory is already being shared." = "このディレクトリはすでに共有されています。";
"Add read only" = "読み出しのみを追加";

// VMConfigAppleSystemView.swift
"CPU Cores" = "CPUコア数";

// VMConfigAppleVirtualizationView.swift
"Enable Balloon Device" = "バルーンデバイスを有効にする";
"Enable Entropy Device" = "エントロピデバイスを有効にする";
"Enable Sound" = "サウンドを有効にする";
"Pointer" = "ポインタ";
"Use Trackpad" = "トラックパッドを使用";
"Allows passing through additional input from trackpads. Only supported on macOS 13+ guests." = "トラックパッドからの追加の入力をパススルーできるようになります。macOS 13以降のゲストでのみ対応しています。";
"Enable Rosetta on Linux (x86_64 Emulation)" = "Linux上でRosettaを有効にする（x86_64エミュレーション）";
"If enabled, a virtiofs share tagged 'rosetta' will be available on the Linux guest for installing Rosetta for emulating x86_64 on ARM64." = "有効にすると、“rosetta”とタグ付けされたVirtioFS共有がLinuxゲストで使用できるようになり、ARM64でx86_64をエミュレートするためにRosettaをインストールできます。";
"Enable Clipboard Sharing" = "クリップボード共有を有効にする";
"Requires SPICE guest agent tools to be installed." = "SPICEゲストエージェントツールがインストールされている必要があります。";

// VMConfigNetworkPortForwardView.swift
"Protocol" = "プロトコル";
"Guest Address" = "ゲストアドレス";
"Guest Port" = "ゲストポート";
"Host Address" = "ホストアドレス";
"Host Port" = "ホストポート";
"New…" = "新規…";

// VMSessionState.swift
"Connection to the server was lost." = "サーバへの接続が切断されました。";
"Background task is about to expire" = "バックグラウンドタスクがまもなく期限切れになります";
"Switch back to UTM to avoid termination." = "終了を防ぐには、UTMに戻ってください。";

// VMConfigQEMUArgumentsView.swift
"Arguments" = "引数";
"Export QEMU Command…" = "QEMUコマンドを書き出す…";
"Export all arguments as a text file. This is only for debugging purposes as UTM's built-in QEMU differs from upstream QEMU in supported arguments." = "すべての引数をテキストファイルとして書き出します。UTMの内蔵QEMUは、対応している引数がアップストリームのQEMUのものと異なるため、この機能はデバッグの目的でのみ使用します。";
"Move Up" = "上に移動";
"Move Down" = "下に移動";

// VMDrivesSettingsView.swift
"Add a new drive." = "新規ドライブを追加します。";
"Browse…" = "選択…";
"Import…" = "読み込む…";
"Select an existing disk image." = "既存のディスクイメージを選択します。";
"Create" = "作成";
"Create an empty drive." = "空のドライブを作成します。";
"%@ Image" = "%@イメージ";

// VMAppleRemovableDrivesView.swift
"Remove" = "削除";
"Shared Directory" = "共有ディレクトリ";
"External Drive" = "外部ドライブ";
"New Shared Directory…" = "新規共有ディレクトリ…";
"(empty)" = "（空）";

// VMAppleSettingsView.swift
"Boot" = "起動";
"Virtualization" = "仮想化";

// VMAppleSettingsAddDeviceMenuView.swift
"Add a new device." = "新規ドライブを追加します。";

// VMWizardView.swift
"Go Back" = "戻る";

// SavePanel.swift
"Select where to save debug log:" = "デバッグログの保存先を選択してください:";
"Select where to save UTM Virtual Machine:" = "UTM仮想マシンの保存先を選択してください:";
"Select where to export QEMU command:" = "QEMUコマンドの書き出し先を選択してください:";


/* Platform/visionOS */

// VMToolbarOrnamentModifier.swift
"Hide Controls" = "コントロールを非表示";
"Show Controls" = "コントロールを表示";


/* Platform/Shared */

// DestructiveButton.swift
"Test" = "テスト";

// DetailedSection.swift
"Section" = "セクション";
"Description" = "説明";

// BusyOverlay.swift
"Download VM" = "仮想マシンをダウンロード";
"Do you want to download '%@'?" = "“%@”をダウンロードしますか?";

// ContentView.swift
"Your version of iOS does not support running VMs while unmodified. You must either run UTM while jailbroken or with a remote debugger attached. See https://getutm.app/install/ for more details." = "お使いのiOSバージョンは改造されていない状態での仮想マシンの実行に対応していません。脱獄（ジェイルブレイク）、またはリモートデバッガに接続された状態でUTMを実行する必要があります。詳細については、https://getutm.app/install/ を参照してください。";

// DefaultTextField.swift
"Prompt" = "プロンプト";

// FileBrowseField.swift
"Path" = "パス";

// RAMSlider.swift
"Size" = "サイズ";
"MiB" = "MiB";

// SizeTextField.swift
"The amount of storage to allocate for this image. Ignored if importing an image. If this is a raw image, then an empty file of this size will be stored with the VM. Otherwise, the disk image will dynamically expand up to this size." = "このイメージに割り当てるストレージ領域です。イメージを読み込む場合は無視されます。生イメージの場合、このサイズの空のファイルが仮想マシンに保存されます。そうでない場合、ディスクイメージはこのサイズまで動的に拡張されます。";
"GiB" = "GiB";

// VMCardView.swift
"Run" = "実行";

// VMCommands.swift
"Open…" = "開く…";
"What's New" = "最新情報";
"Virtual Machine Gallery" = "仮想マシンギャラリー";
"Support" = "サポート";
"License" = "ライセンス";

// VMConfigConstantPicker.swift
"Selected:" = "選択中:";
"Text" = "テキスト";

// VMConfigDisplayView.swift
"Hardware" = "ハードウェア";
"Emulated Display Card" = "仮想ディスプレイカード";
"GPU Acceleration Supported" = "GPUアクセラレーションに対応";
"Guest drivers are required for 3D acceleration." = "3Dアクセラレーションには、ゲストドライバが必要です。";
"VGA Device RAM (MB)" = "VGAデバイスRAM（MB）";
"Auto Resolution" = "解像度を自動的に設定";
"Resize display to window size automatically" = "ディスプレイサイズをウインドウサイズに自動的に合わせる";
"Resize display to screen size and orientation automatically" = "ディスプレイサイズを画面サイズと向きに自動的に合わせる";
"Scaling" = "拡大/縮小";
"Upscaling" = "拡大";
"Downscaling" = "縮小";
"Retina Mode" = "Retinaモード";

// VMConfigDisplayConsoleView.swift
"Style" = "スタイル";
"Theme" = "テーマ";
"Text Color" = "文字色";
"Background Color" = "背景色";
"Font" = "フォント";
"Font Size" = "フォントサイズ";
"Blinking cursor?" = "点滅カーソル";
"Resize Console Command" = "コンソールサイズ変更コマンド";
"Command to send when resizing the console. Placeholder $COLS is the number of columns and $ROWS is the number of rows." = "コンソールのサイズを変更するときに送信するコマンドです。$COLSは列数、$ROWSは行数に置き換えられます。";
"stty cols $COLS rows $ROWS\n" = "stty cols $COLS rows $ROWS\n";

// VMConfigDriveCreateView.swift
"If checked, no drive image will be stored with the VM. Instead you can mount/unmount image while the VM is running." = "チェックを入れると、ドライブイメージは仮想マシンに保存されなくなります。その代わり、仮想マシン実行中にイメージのマウント/マウント解除が可能になります。";
"Hardware interface on the guest used to mount this image. Different operating systems support different interfaces. The default will be the most common interface." = "このイメージをマウントするために使用されるゲスト上のハードウェアインターフェイスです。オペレーティングシステムによって対応しているインターフェイスは異なります。デフォルトでは最も一般的なインターフェイスが使用されます。";
"Raw Image" = "生イメージ";
"Advanced. If checked, a raw disk image is used. Raw disk image does not support snapshots and will not dynamically expand in size." = "詳細オプションです。チェックを入れると、生ディスクイメージが使用されます。生ディスクイメージはスナップショットに対応しておらず、サイズを動的に拡張することはありません。";

// VMConfigDriveDetailsView.swift
"(new)" = "（新規）";
"Image Type" = "イメージの種類";
"Update Interface" = "インターフェイスをアップデート";
"Older versions of UTM added each IDE device to a separate bus. Check this to change the configuration to place two units on each bus." = "UTMの古いバージョンでは、IDEデバイスをそれぞれ別のバスに追加していました。これにチェックを入れると、各バスに2つのユニットを配置する構成に変更できます。";
"Reclaim Space" = "領域を回収";
"Reclaim disk space by re-converting the disk image." = "ディスクイメージを再変換することでディスク領域を回収します。";
"Compress" = "圧縮";
"Compress by re-converting the disk image and compressing the data." = "ディスクイメージの再変換およびデータの圧縮を行うことで圧縮します。";
"Resize…" = "サイズを変更…";
"Increase the size of the disk image." = "ディスクイメージのサイズを大きくします。";
"Would you like to re-convert this disk image to reclaim unused space? Note this will require enough temporary space to perform the conversion. You are strongly encouraged to back-up this VM before proceeding." = "このディスクイメージを再変換して未使用の領域を回収しますか? 変換を行うには、一時的に十分な空き領域が必要になることに注意してください。この作業を行う前に、仮想マシンをバックアップしておくことを強く推奨します。";
"Reclaim" = "回収";
"Would you like to re-convert this disk image to reclaim unused space and apply compression? Note this will require enough temporary space to perform the conversion. Compression only applies to existing data and new data will still be written uncompressed. You are strongly encouraged to back-up this VM before proceeding." = "このディスクイメージを再変換して未使用の領域を回収し、圧縮を適用しますか? 変換を行うには、一時的に十分な空き領域が必要になることに注意してください。圧縮は既存のデータにのみ適用され、新しいデータは圧縮されずに書き込まれます。この作業を行う前に、仮想マシンをバックアップしておくことを強く推奨します。";
"Resizing is experimental and could result in data loss. You are strongly encouraged to back-up this VM before proceeding. Would you like to resize to %lld GiB?" = "サイズの変更は実験的であり、データが失われる可能性があります。この作業を行う前に、仮想マシンをバックアップしておくことを強く推奨します。%lld GiBにサイズを変更しますか?";
"Minimum size: %@" = "最小サイズ: %@";
"Resize" = "サイズを変更";
"Calculating current size..." = "現在のサイズを計算中…";

// VMConfigInfoView.swift
"Generic" = "一般";
"Notes" = "メモ";
"Icon" = "アイコン";
"Choose" = "選択";
"AIX" = "AIX";
"iOS" = "iOS";
"Windows 7" = "Windows 7";
"AlmaLinux" = "AlmaLinux";
"Alpine" = "Alpine";
"AmigaOS" = "AmigaOS";
"Android" = "Android";
"Apple TV" = "Apple TV";
"Arch Linux" = "Arch Linux";
"BackTrack" = "BackTrack";
"Bada" = "Bada";
"BeOS" = "BeOS";
"CentOS" = "CentOS";
"Chrome OS" = "Chrome OS";
"CyanogenMod" = "CyanogenMod";
"Debian" = "Debian";
"Elementary OS" = "elementary OS";
"Fedora" = "Fedora";
"Firefox OS" = "Firefox OS";
"FreeBSD" = "FreeBSD";
"Gentoo" = "Gentoo";
"Haiku OS" = "Haiku OS";
"HP-UX" = "HP-UX";
"KaiOS" = "KaiOS";
"Knoppix" = "Knoppix";
"Kubuntu" = "Kubuntu";
"Linux" = "Linux";
"Lubuntu" = "Lubuntu";
"macOS" = "macOS";
"Maemo" = "Maemo";
"Mandriva" = "Mandriva";
"MeeGo" = "MeeGo";
"Linux Mint" = "Linux Mint";
"NetBSD" = "NetBSD";
"Nintendo" = "任天堂";
"NixOS" = "NixOS";
"OpenBSD" = "OpenBSD";
"OpenWrt" = "OpenWrt";
"OS/2" = "OS/2";
"Palm OS" = "Palm OS";
"PlayStation Portable" = "PlayStation Portable";
"PlayStation" = "PlayStation";
"Pop!_OS" = "Pop!_OS";
"Red Hat" = "Red Hat";
"Remix OS" = "Remix OS";
"RISC OS" = "RISC OS";
"Sabayon" = "Sabayon";
"Sailfish OS" = "Sailfish OS";
"Slackware" = "Slackware";
"Solaris" = "Solaris";
"openSUSE" = "openSUSE";
"Syllable" = "Syllable";
"Symbian" = "Symbian";
"ThreadX" = "ThreadX";
"Tizen" = "Tizen";
"Ubuntu" = "Ubuntu";
"webOS" = "webOS";
"Windows 11" = "Windows 11";
"Windows 9x" = "Windows 9x系";
"Windows XP" = "Windows XP";
"Windows" = "Windows";
"Xbox" = "Xbox";
"Xubuntu" = "Xubuntu";
"YunOS" = "YunOS";

// VMConfigInputView.swift
"If enabled, the default input devices will be emulated on the USB bus." = "有効にすると、デフォルトの入力デバイスがUSBバス上でエミュレートされます。";
"USB Support" = "対応USB";
"USB Sharing" = "USB共有";
"USB sharing not supported in this build of UTM." = "USB共有はこのビルドのUTMでは対応していません。";
"Share USB devices from host" = "ホストからUSBデバイスを共有";
"Maximum Shared USB Devices" = "最大共有USBデバイス数";
"Additional Settings" = "その他の設定";
"Gesture and Cursor Settings" = "ジェスチャとカーソルの設定";

// VMConfigNetworkView.swift
"Bridged Interface" = "ブリッジインターフェイス";
"Emulated Network Card" = "仮想ネットワークカード";
"Show Advanced Settings" = "詳細設定を表示";
"IP Configuration" = "IP構成";

// VMConfigAdvancedNetworkView.swift
"Isolate Guest from Host" = "ゲストをホストから隔離";
"Guest Network" = "ゲストネットワーク";
"Guest Network (IPv6)" = "ゲストネットワーク（IPv6）";
"Host Address (IPv6)" = "ホストアドレス（IPv6）";
"DHCP Start" = "DHCP割り当て開始アドレス";
"DHCP End" = "DHCP割り当て終了アドレス";
"DHCP Domain Name" = "DHCPドメイン名";
"DNS Server" = "DNSサーバ";
"DNS Server (IPv6)" = "DNSサーバ（IPv6）";
"DNS Search Domains" = "DNS検索ドメイン";

// VMConfigQEMUView.swift
"Logging" = "ログ";
"Debug Logging" = "デバッグログ";
"Export Debug Log" = "デバッグログを書き出す";
"Tweaks" = "調整";
"These are advanced settings affecting QEMU which should be kept default unless you are running into issues." = "これらはQEMUに影響を与える詳細設定であり、問題が発生しない限りデフォルトのままにしておくべきです。";
"UEFI Boot" = "UEFI起動";
"Should be off for older operating systems such as Windows 7 or lower." = "Windows 7以前など、古いオペレーティングシステムに対してはオフにしておく必要があります。";
"RNG Device" = "RNGデバイス";
"Should be on always unless the guest cannot boot because of this." = "これが原因でゲストが起動できない場合を除き、常にオンにしておくべきです。";
"Balloon Device" = "バルーンデバイス";
"TPM 2.0 Device" = "TPM 2.0デバイス";
"TPM can be used to protect secrets in the guest operating system. Note that the host will always be able to read these secrets and therefore no expectation of physical security is provided." = "TPMは、ゲストオペレーティングシステムの秘密を保護するために使用できます。なお、ホストは常にこれらの秘密を読み取ることができるため、物理的なセキュリティは期待できないことに注意してください。";
"Use Hypervisor" = "ハイパーバイザを使用";
"Only available if host architecture matches the target. Otherwise, TCG emulation is used." = "ホストアーキテクチャがターゲットと一致する場合のみ利用できます。そうでない場合、TCGエミュレーションが使用されます。";
"Use TSO" = "TSOを使用";
"Only available when Hypervisor is used on supported hardware. TSO speeds up Intel emulation in the guest at the cost of decreased performance in general." = "対応ハードウェア上でハイパーバイザが使用されている場合にのみ利用可能です。TSOは、ゲスト内のIntelエミュレーションを高速化させますが、その代償として一般的なパフォーマンスを低下させます。";
"Use local time for base clock" = "ベースクロックにローカル時間を使用";
"If checked, use local time for RTC which is required for Windows. Otherwise, use UTC clock." = "チェックを入れると、Windowsで必要とされるRTCにローカル時間を使用します。そうでない場合、UTCクロックを使用します。";
"Force PS/2 controller" = "PS/2コントローラを強制";
"Instantiate PS/2 controller even when USB input is supported. Required for older Windows." = "USB入力に対応している場合でも、PS/2コントローラをインスタンス化します。古いWindowsで必要です。";
"Maintenance" = "修復";
"Options here only apply on next boot and are not saved." = "これらのオプションは次回起動時にのみ適用され、保存されません。";
"Reset UEFI Variables" = "UEFI変数をリセット";
"You can use this if your boot options are corrupted or if you wish to re-enroll in the default keys for secure boot." = "起動オプションが破損している場合や、セキュアブートのデフォルトキーに再登録したい場合に使用できます。";
"QEMU Machine Properties" = "QEMUマシンプロパティ";
"This is appended to the -machine argument." = "-machine引数に追加されます。";
"QEMU Arguments" = "QEMU引数";
"(Delete)" = "（削除）";

// VMConfigSerialView.swift
"Target" = "ターゲット";
"Wait for Connection" = "接続を待機";
"Allow Remote Connection" = "リモート接続を許可";
"Emulated Serial Device" = "仮想シリアルデバイス";
"TCP" = "TCP";
"Server Address" = "サーバアドレス";
"The target does not support hardware emulated serial connections." = "このターゲットはハードウェア仮想シリアル接続に対応していません。";

// VMConfigSharingView.swift
"Clipboard Sharing" = "クリップボード共有";
"WebDAV requires installing SPICE daemon. VirtFS requires installing device drivers." = "WebDAVはSPICEデーモン、VirtFSはデバイスドライバのインストールが必要です。";
"Directory Share Mode" = "ディレクトリ共有モード";

// VMConfigSoundView.swift
"Emulated Audio Card" = "仮想オーディオカード";
"This audio card is not supported." = "このオーディオカードは対応していません。";

// VMConfigSystemView.swift
"CPU" = "CPU";
"Force Enable CPU Flags" = "CPUフラグを強制的に有効にする";
"If checked, the CPU flag will be enabled. Otherwise, the default value will be used." = "チェックを入れると、CPUフラグが有効になります。そうでない場合、デフォルトの値が使用されます。";
"Force Disable CPU Flags" = "CPUフラグを強制的に無効にする";
"If checked, the CPU flag will be disabled. Otherwise, the default value will be used." = "チェックを入れると、CPUフラグが無効になります。そうでない場合、デフォルトの値が使用されます。";
"Force multicore may improve speed of emulation but also might result in unstable and incorrect emulation." = "マルチコアを強制すると、エミュレーションの速度は向上しますが、エミュレーションが不安定になったり、正しく実行されなかったりする可能性があります。";
"Cores" = "コア";
"Force Multicore" = "マルチコアを強制";
"JIT Cache" = "JITキャッシュ";
"Default is 1/4 of the RAM size (above). The JIT cache size is additive to the RAM size in the total memory usage!" = "デフォルトはRAMサイズ（上記）の4分の1です。JITキャッシュサイズは合計メモリ使用量のRAMサイズに加算されます!";
"Allocating too much memory will crash the VM." = "メモリを過剰に割り当てると、仮想マシンがクラッシュします。";
"This change will reset all settings" = "この変更により、すべての設定がリセットされます";
"Your device has %llu MB of memory and the estimated usage is %llu MB." = "お使いのデバイスは%1$llu MBのメモリを搭載しており、推定使用量は%2$llu MBです。";
"Any unsaved changes will be lost." = "保存されていない変更内容は失われます。";
"Architecture" = "アーキテクチャ";
"The selected architecture is unsupported in this version of UTM." = "選択されたアーキテクチャはこのバージョンのUTMでは対応していません。";
"Hide Unused…" = "未使用を非表示…";
"Show All…" = "すべてを表示…";

// VMConfirmActionModifier.swift
"Do you want to copy this VM and all its data to internal storage?" = "この仮想マシン、およびそのすべてのデータを内部ストレージにコピーしますか?";
"Do you want to duplicate this VM and all its data?" = "この仮想マシン、およびそのすべてのデータを複製しますか?";
"Do you want to delete this VM and all its data?" = "この仮想マシン、およびそのすべてのデータを削除しますか?";
"Do you want to remove this shortcut? The data will not be deleted." = "このショートカットを削除しますか? データは削除されません。";
"Do you want to force stop this VM and lose all unsaved data?" = "この仮想マシンを強制停止しますか? 保存されていないデータは失われます。";
"Do you want to move this VM to another location? This will copy the data to the new location, delete the data from the original location, and then create a shortcut." = "この仮想マシンを別の場所に移動しますか? この操作により、データを新しい場所にコピーし、元の場所のデータを削除した後、ショートカットが作成されます。";

// VMContextMenuModifier.swift
"Show in Finder" = "Finderに表示";
"Reveal where the VM is stored." = "仮想マシンが格納されている場所を表示します。";
"Edit" = "編集";
"Modify settings for this VM." = "この仮想マシンの設定を変更します。";
"Stop the running VM." = "実行中の仮想マシンを停止します。";
"Run the VM in the foreground." = "仮想マシンをフォアグラウンドで実行します。";
"Run Recovery" = "“復旧”を実行";
"Boot into recovery mode." = "復旧モードで起動します。";
"Run without saving changes" = "変更内容を保存せずに実行";
"Run the VM in the foreground, without saving data changes to disk." = "データの変更内容をディスクに保存せずに、仮想マシンをフォアグラウンドで実行します。";
"Download and mount the guest tools for Windows." = "Windows用ゲストツールをダウンロードし、マウントします。";
"Share…" = "共有…";
"Share a copy of this VM and all its data." = "この仮想マシン、およびそのすべてのデータのコピーを共有します。";
"Move…" = "移動…";
"Move this VM from internal storage to elsewhere." = "この仮想マシンを内部ストレージからほかの場所に移動します。";
"Clone…" = "複製…";
"Duplicate this VM along with all its data." = "この仮想マシン、およびそのすべてのデータを複製します。";
"New from template…" = "テンプレートから新規作成…";
"Create a new VM with the same configuration as this one but without any data." = "この仮想マシンと同じ構成の、データを含まない新規仮想マシンを作成します。";
"Delete this shortcut. The underlying data will not be deleted." = "このショートカットを削除します。リンク先のデータは削除されません。";
"Delete this VM and all its data." = "この仮想マシン、およびそのすべてのデータを削除します。";

// VMDetailsView.swift
"This virtual machine has been removed." = "この仮想マシンは削除されました。";
"Status" = "状態";
"Architecture" = "アーキテクチャ";
"Machine" = "マシン";
"Memory" = "メモリ";
"Serial (TTY)" = "シリアル（TTY）";
"Serial (Client)" = "シリアル（クライアント）";
"Serial (Server)" = "シリアル（サーバ）";
"Inactive" = "非アクティブ";

// VMNavigationListView.swift
"Donate" = "寄付";
"Pending" = "保留中";
"New VM" = "新規仮想マシン";
"Create a new VM" = "新規仮想マシンを作成します";

// VMPlaceholderView.swift
"Welcome to UTM" = "ようこそUTMへ";
"Create a New Virtual Machine" = "新規仮想マシンを作成";
"Browse UTM Gallery" = "UTMギャラリーをブラウズ";
"User Guide" = "ユーザガイド";
"Support" = "サポート";
"Server" = "サーバ";

// VMRemovableDrivesView.swift
"%@ %@" = "%1$@ %2$@";

// VMSettingsAddDeviceMenuView.swift
"Import Drive…" = "ドライブを読み込む…";
"New Drive…" = "新規ドライブ…";

// VMToolbarModifier.swift
"Remove selected shortcut" = "選択したショートカットを削除します";
"Delete selected VM" = "選択した仮想マシンを削除します";
"Clone" = "複製";
"Clone selected VM" = "選択した仮想マシンを複製します";
"Move" = "移動";
"Move selected VM" = "選択した仮想マシンを移動します";
"Share" = "共有";
"Share selected VM" = "選択した仮想マシンを共有します";
"Stop selected VM" = "選択した仮想マシンを停止します";
"Run selected VM" = "選択した仮想マシンを実行します";
"Edit selected VM" = "選択した仮想マシンを編集します";
"Preferences" = "環境設定";
"Show UTM preferences" = "UTM環境設定を表示します";

// VMWizardDrivesView.swift
"Storage" = "ストレージ";
"Specify the size of the drive where data will be stored into." = "データを保存するドライブのサイズを指定してください。";

// VMWizardHardwareView.swift
"Hardware OpenGL Acceleration" = "ハードウェアOpenGLアクセラレーション";
"There are known issues in some newer Linux drivers including black screen, broken compositing, and apps failing to render." = "一部の新しいLinuxドライバの中には、画面が黒くなる、表示が乱れる、アプリのレンダリングに失敗するといった既知の問題があります。";
"Enable hardware OpenGL acceleration" = "ハードウェアOpenGLアクセラレーションを有効にする";

// VMWizardOSLinuxView.swift
"Virtualization Engine" = "仮想化エンジン";
"Apple Virtualization is experimental and only for advanced use cases. Leave unchecked to use QEMU, which is recommended." = "Apple仮想化は実験的であり、高度なユースケースにのみ使用します。推奨されるQEMUを使用するには、チェックを外したままにします。";
"Use Apple Virtualization" = "Apple仮想化を使用";
"Boot from kernel image" = "カーネルイメージから起動";
"If set, boot directly from a raw kernel image and initrd. Otherwise, boot from a supported ISO." = "有効にすると、生のカーネルイメージと初期RAMディスクから直接起動します。そうでない場合、対応したISOから起動します。";
"Debian Install Guide" = "Debianインストールガイド";
"Ubuntu Install Guide" = "Ubuntuインストールガイド";
"Boot Image Type" = "起動イメージの種類";
"Enable Rosetta (x86_64 Emulation)" = "Rosettaを有効にする（x86_64エミュレーション）";
"Installation Instructions" = "インストール手順";
"Additional Options" = "その他のオプション";
"Uncompressed Linux kernel (required)" = "無圧縮Linuxカーネル（必須）";
"Linux kernel (required)" = "Linuxカーネル（必須）";
"Uncompressed Linux initial ramdisk (optional)" = "無圧縮Linux初期RAMディスク（オプション）";
"Linux initial ramdisk (optional)" = "Linux初期RAMディスク（オプション）";
"Linux Root FS Image (optional)" = "Linux RootFSイメージ（オプション）";
"Boot ISO Image (optional)" = "起動ISOイメージ（オプション）";
"Boot ISO Image" = "起動ISOイメージ";

// VMWizardOSMacView.swift
"To install macOS, you need to download a recovery IPSW. If you do not select an existing IPSW, the latest macOS IPSW will be downloaded from Apple." = "macOSをインストールするには、復元IPSWをダウンロードする必要があります。既存のIPSWを選択しない場合、Appleから最新のmacOSのIPSWがダウンロードされます。";
"Drag and drop IPSW file here" = "ここにIPSWファイルをドラッグアンドドロップ";
"Import IPSW" = "IPSWを読み込む";
"macOS guests are only supported on ARM64 devices." = "macOSゲストはARM64デバイスでのみ対応しています。";

// VMWizardOSOtherView.swift
"Other" = "その他";
"Boot Device" = "起動デバイス";
"CD/DVD Image" = "CD/DVDイメージ";
"Floppy Image" = "フロッピーイメージ";
"Boot IMG Image" = "起動IMGイメージ";
"Legacy Hardware" = "レガシーハードウェア";
"If checked, emulated devices with higher compatibility will be instantiated at the cost of performance." = "チェックを入れると、パフォーマンスを犠牲にして、より互換性の高い仮想デバイスがインスタンス化されます。";
"Options" = "オプション";

// VMWizardOSView.swift
"macOS 12+" = "macOS 12以降";
"Windows" = "Windows";
"Preconfigured" = "構成済み";

// VMWizardOSWindowsView.swift
"Install Windows 10 or higher" = "Windows 10以降をインストール";
"Import VHDX Image" = "VHDXイメージを読み込む";
"Download Windows 11 for ARM64 Preview VHDX" = "ARM64用のWindows 11プレビューVHDXをダウンロード";
"Fetch latest Windows installer…" = "最新のWindowsインストーラを取得…";
"Windows Install Guide" = "Windowsインストールガイド";
"Image File Type" = "イメージファイルの種類";
"Boot VHDX Image" = "起動VHDXイメージ";
"Some older systems do not support UEFI boot, such as Windows 7 and below." = "Windows 7以前など、一部の古いシステムはUEFI起動に対応していません。";
"Secure Boot with TPM 2.0" = "TPM 2.0によるセキュアブート";
"Download and mount the guest support package for Windows. This is required for some features including dynamic resolution and clipboard sharing." = "Windows用ゲストサポートパッケージをダウンロードし、マウントします。ダイナミック解像度やクリップボード共有など、一部の機能を使用する場合に必要です。";
"Install drivers and SPICE tools" = "ドライバとSPICEツールをインストール";

// VMWizardSharingView.swift
"Shared Directory Path" = "共有ディレクトリパス";
"Optionally select a directory to make accessible inside the VM. Note that support for shared directories varies by the guest operating system and may require additional guest drivers to be installed. See UTM support pages for more details." = "仮想マシン内部でアクセスできるようにするディレクトリを選択してください（オプション）。共有ディレクトリの対応はゲストのオペレーティングシステムによって異なり、追加のゲストドライバのインストールが必要になる場合があることに注意してください。詳細については、UTMサポートページを参照してください。";
"Share is read only" = "共有を読み出しのみにする";

// VMWizardStartView.swift
"Virtualize" = "仮想化";
"Faster, but can only run the native CPU architecture." = "高速ですが、ネイティブのCPUアーキテクチャのみ実行することができます。";
"Emulate" = "エミュレート";
"Slower, but can run other CPU architectures." = "低速ですが、ほかのCPUアーキテクチャを実行することができます。";
"Virtualization is not supported on your system." = "仮想化はお使いのシステムでは対応していません。";
"This build does not emulation." = "このビルドではエミュレーションを行いません。";
"Download prebuilt from UTM Gallery…" = "UTMギャラリーからビルド済みパッケージをダウンロード…";
"Existing" = "既存";

// VMWizardStartViewTCI.swift
"New Machine" = "新規マシン";
"Create a new emulated machine from scratch." = "新規仮想マシンをゼロから作成します。";

// VMWizardState.swift
"Please select a boot image." = "起動イメージを選択してください。";
"Please select a kernel file." = "カーネルファイルを選択してください。";
"Failed to get latest macOS version from Apple." = "Appleから最新のmacOSバージョンの取得に失敗しました。";
"macOS is not supported with QEMU." = "macOSはQEMUには対応していません。";
"Unavailable for this platform." = "このプラットフォームでは利用できません。";
"The selected boot image contains the word '%@' but the guest architecture is '%@'. Please ensure you have selected an image that is compatible with '%@'." = "選択された起動イメージには単語“%1$@”が含まれていますが、ゲストアーキテクチャは“%2$@”になっています。“%3$@”と互換性のあるイメージが選択されていることを確認してください。";

// VMWizardSummaryView.swift
"Default Cores" = "デフォルトのコア数";
"Summary" = "概要";
"Open VM Settings" = "仮想マシン設定を開く";
"Engine" = "エンジン";
"Apple Virtualization" = "Apple仮想化";
"Use Virtualization" = "仮想化を使用";
"RAM" = "RAM";
"Skip Boot Image" = "起動イメージをスキップ";
"Boot Image" = "起動イメージ";
"IPSW" = "IPSW";
"Kernel" = "カーネル";
"Initial Ramdisk" = "初期RAMディスク";
"Root Image" = "Rootイメージ";
"Use Rosetta" = "Rosettaを使用";
"Share Directory" = "ディレクトリを共有";
"Directory" = "ディレクトリ";

// VMReleaseNotesView.swift
"No release notes found for version %@." = "バージョン%@のリリースノートが見つかりません。";
"Show All" = "すべてを表示";
"\u2022 " = "\u2022 ";

// UTMPendingVMView.swift
"Extracting…" = "展開中…";
"%1$@ of %2$@ (%3$@)" = "%1$@ / %2$@（%3$@）";
"Preparing…" = "準備中…";
"Cancel Download" = "ダウンロードをキャンセル";

// UTMUnavailableVMView.swift
"This virtual machine must be re-added to UTM by opening it with Finder. You can find it at the path: %@" = "この仮想マシンはFinderで開いてUTMに再度追加する必要があります。次のパスにあります: %@";
"This virtual machine cannot be found at: %@" = "この仮想マシンは次の場所で見つけられません: %@";

// UTMTips.swift
"Support UTM" = "UTMを支援";
"Enjoying the app? Consider making a donation to support development." = "アプリをお楽しみいただけていますか? 開発を支援するために寄付をご検討ください。";
"No Thanks" = "今はしない";
"Tap to hide/show toolbar" = "タップしてツールバーを非表示/表示";
"When the toolbar is hidden, the icon will disappear after a few seconds. To show the icon again, tap anywhere on the screen." = "ツールバーを非表示にすると、アイコンが数秒後に消えます。アイコンを再度表示するには、画面上の任意の場所をタップします。";
"Start Here" = "ここから始めましょう";
"Create a new virtual machine or import an existing one." = "新しい仮想マシンを作成するか、既存の仮想マシンを読み込みます。";


/* Platform */

// UTMData.swift
"An existing virtual machine already exists with this name." = "この名前の仮想マシンがすでに存在します。";
"This virtual machine is currently unavailable, make sure it is not open in another session." = "この仮想マシンは現在使用できません。別のセッションで開かれていないことを確認してください。";
"Failed to clone VM." = "仮想マシンの複製に失敗しました。";
"Unable to add a shortcut to the new location." = "新しい場所にショートカットを追加できません。";
"Cannot import this VM. Either the configuration is invalid, created in a newer version of UTM, or on a platform that is incompatible with this version of UTM." = "この仮想マシンは読み込めません。構成が無効であるか、新しいバージョンのUTM、またはこのバージョンのUTMと互換性のないプラットフォームで作成されています。";
"Failed to parse imported VM." = "読み込まれた仮想マシンの解析に失敗しました。";
"Cannot find AltServer for JIT enable. You cannot run VMs until JIT is enabled." = "JITを有効にするためのAltServerを見つけられません。JITが有効になるまで仮想マシンを実行できません。";
"AltJIT error: %@" = "AltJITエラー: %@";
"Failed to attach to JitStreamer:\n%@" = "JitStreamerへのアタッチに失敗しました:\n%@";
"Failed to decode JitStreamer response." = "JitStreamerの応答のデコードに失敗しました。";
"Failed to attach to JitStreamer." = "JitStreamerへのアタッチに失敗しました。";
"Invalid JitStreamer attach URL:\n%@" = "JitStreamerアタッチURLが無効です:\n%@";
"This functionality is not yet implemented." = "この機能はまだ実装されていません。";
"Failed to reconnect to the server." = "サーバへの再接続に失敗しました。";

// UTMDownloadVMTask.swift
"There is no UTM file in the downloaded ZIP archive." = "ダウンロードされたZIPアーカイブ内にUTMファイルがありません。";
"Failed to parse the downloaded VM." = "ダウンロードされた仮想マシンの解析に失敗しました。";

// UTMDownloadSupportToolsTask.swift
"Windows Guest Support Tools" = "Windowsゲストサポートツール";
"No empty removable drive found. Make sure you have at least one removable drive that is not in use." = "空のリムーバブルドライブが見つかりません。使用していないリムーバブルドライブが少なくとも1台あることを確認してください。";
"The guest support tools have already been mounted." = "ゲストサポートツールはすでにマウントされています。";

// UTMDownloadMacSupportToolsTask.swift
"macOS Guest Support Tools" = "macOSゲストサポートツール";

// UTMPendingVirtualMachine.swift
"%@ remaining" = "残り%@";
"%@/s" = "%@/秒";

// VMData.swift
"(Unavailable)" = "（利用不可）";
"Virtual machine not loaded." = "仮想マシンが読み込まれていません。";
"Unavailable" = "利用不可";
"Suspended" = "サスペンド済み";
"Stopped" = "停止済み";
"Starting" = "開始中";
"Started" = "開始済み";
"Pausing" = "一時停止中";
"Paused" = "一時停止済み";
"Resuming" = "再開中";
"Stopping" = "停止中";
"Saving" = "保存中";
"Restoring" = "復元中";
"This function is not implemented." = "この機能は実装されていません。";
"This VM is not available or is configured for a backend that does not support remote clients." = "この仮想マシンは利用できないか、リモートクライアントに対応していないバックエンド用に構成されています。";


/* Remote */

// UTMRemoteKeyManager.swift
"Failed to generate a key pair." = "鍵ペアの生成に失敗しました。";
"Failed to parse generated key pair." = "生成された鍵ペアの解析に失敗しました。";
"Failed to import generated key." = "生成された鍵の読み込みに失敗しました。";

// UTMRemoteClient.swift
"Failed to determine host name." = "ホスト名の特定に失敗しました。";
"Failed to get host fingerprint." = "ホストの指紋の取得に失敗しました。";
"Password is required." = "パスワードが必要です。";
"Password is incorrect." = "パスワードが間違っています。";
"This host is not yet trusted. You should verify that the fingerprints match what is displayed on the host and then select Trust to continue." = "このホストはまだ信頼されていません。指紋がホストに表示されているものと一致していることを確認し、“信頼”を選択して続ける必要があります。";
"The server interface version does not match the client." = "サーバインターフェイスのバージョンがクライアントと一致しません。";

// UTMRemoteSpiceVirtualMachine.swift
"Failed to connect to SPICE: %@" = "SPICEへの接続に失敗しました: %@";
"An operation is already in progress." = "操作はすでに進行中です。";

// UTMRemoteServer.swift
"Allow" = "許可";
"Deny" = "拒否";
"Disconnect" = "切断";
"New unknown remote client connection." = "新しい不明なリモートクライアントからの接続がありました。";
"New trusted remote client connection." = "新しい信頼済みリモートクライアントからの接続がありました。";
"Unknown Remote Client" = "不明なリモートクライアント";
"A client with fingerprint '%@' is attempting to connect." = "指紋“%@”のクライアントが接続しようとしています。";
"Remote Client Connected" = "リモートクライアント接続済み";
"Established connection from %@." = "%@からの接続を確立しました。";
"UTM Remote Server Error" = "UTMリモートサーバエラー";
"Cannot reserve port %d for external access from NAT. Make sure no other device on the network has reserved it." = "NATからの外部アクセス用のポート%dを予約できません。ネットワーク上のほかのデバイスが予約していないことを確認してください。";
"Not authenticated." = "認証されていません。";
"The client interface version does not match the server." = "クライアントインターフェイスのバージョンがサーバと一致しません。";
"Cannot find VM with ID: %@" = "指定されたIDの仮想マシンが見つかりません: %@";
"Invalid backend." = "バックエンドが無効です。";
"Failed to access file." = "ファイルへのアクセスに失敗しました。";


/* Scripting */

// UTMScriptingUSBDeviceImpl.swift
"UTM is not ready to accept commands." = "UTMはコマンドを受け入れる準備ができていません。";
"The device cannot be found." = "デバイスが見つかりません。";
"The device is not currently connected." = "デバイスが現在接続されていません。";

// UTMScriptingVirtualMachineImpl.swift
"Operation not available." = "操作は利用できません。";
"Operation not supported by the backend." = "操作はバックエンドが対応していません。";
"The virtual machine is not running." = "仮想マシンが実行されていません。";
"The virtual machine must be stopped before this operation can be performed." = "この操作を実行するには、仮想マシンを停止する必要があります。";
"The QEMU guest agent is not running or not installed on the guest." = "QEMUゲストエージェントが起動していないか、ゲストにインストールされていません。";
"One or more required parameters are missing or invalid." = "1つ以上の必須パラメータがないか無効です。";

// UTMScriptingConfigImpl.swift
"Identifier '%@' cannot be found." = "識別子“%@”が見つかりません。";
"Drive description is invalid." = "ドライブの説明が無効です。";
"Index %lld cannot be found." = "インデックス%lldが見つかりません。";
"This device is not supported by the target." = "このデバイスはターゲットが対応していません。";

// UTMScriptingCreateCommand.swift
"A valid backend must be specified." = "有効なバックエンドを指定する必要があります。";
"This backend is not supported on your machine." = "このバックエンドはお使いのマシンでは対応していません。";
"A valid configuration must be specified." = "有効な構成を指定する必要があります。";
"No name specified in the configuration." = "構成に名前が指定されていません。";
"No architecture specified in the configuration." = "構成にアーキテクチャが指定されていません。";

// UTMScriptingImportCommand.swift
"A valid UTM file must be specified." = "有効なUTMファイルを指定する必要があります。";
"No file specified in the command." = "コマンドにファイルが指定されていません。";



/** QEMUKit **/

/* Sources/QEMUKit */

// UTMQemuVirtualMachine.swift
"QEMU exited from an error: %@" = "QEMUがエラーにより終了しました: %@";


/* Sources/QEMUKitInternal */

// UTMQemuGuestAgent.m
"Mismatched id from guest-sync-delimited." = "guest-sync-delimitedからのIDが一致しません。";

// UTMJSONStream.m
"Error parsing JSON." = "JSONの解析中にエラーが発生しました。";
"Port is not connected." = "ポートが接続されていません。";

// UTMQemuManager.m
"Timed out waiting for RPC." = "RPCの待機時間がタイムアウトになりました。";
"Manager being deallocated, killing pending RPC." = "マネージャがメモリ解放されたため、保留中のRPCが強制終了しました。";

// UTMQemuMonitor.m
"Guest panic" = "ゲストがパニック状態に陥りました";
