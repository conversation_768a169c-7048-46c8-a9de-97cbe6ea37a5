/* A removable drive that has no image file inserted. */
"(empty)" = "(空)";

/* VMConfigAppleDriveDetailsView */
"(New Drive)" = "(新驱动器)";

/* No comment provided by engineer. */
"(new)" = "(新)";

/* VMData */
"(Unavailable)" = "(不可用)";

/* QEMUConstant */
"%@ (%@)" = "%1$@ (%2$@)";

/* VMDisplayQemuDisplayController
VMToolbarDriveMenuView */
"%@ (%@): %@" = "%1$@ (%2$@): %3$@";

/* VMDisplayMetalWindowController */
"%@ (Display %lld)" = "%1$@ (显示 %2$lld)";

/* VMDisplayAppleTerminalWindowController
VMDisplayQemuTerminalWindowController */
"%@ (Terminal %lld)" = "%1$@ (终端 %2$lld)";

/* VMRemovableDrivesView */
"%@ %@" = "%1$@ %2$@";

/* No comment provided by engineer. */
"%@ ➡️ %@" = "%1$@ ➡️ %2$@";

/* VMDrivesSettingsView */
"%@ Drive" = "%@ 驱动器";

/* VMDrivesSettingsView */
"%@ Image" = "%@ 映像";

/* Format string for remaining time until a download finishes */
"%@ remaining" = "剩余 %@";

/* Format string for the 'per second' part of a download speed. */
"%@/s" = "%@/s";

/* Format string for download progress and speed, e. g. 5 MB of 6 GB (200 kbit/s) */
"%1$@ of %2$@ (%3$@)" = "共 %2$@，已下载 %1$@ (%3$@)";

/* UTMDonateView */
"%d days" = "%d 天";

/* UTMDonateView */
"%d months" = "%d 月";

/* UTMDonateView */
"%d weeks" = "%d 周";

/* UTMDonateView */
"%d years" = "%d 年";

/* No comment provided by engineer. */
"• " = "• ";

/* UTMScriptingAppDelegate */
"A valid backend must be specified." = "必须指定有效的后端。";

/* UTMScriptingAppDelegate */
"A valid configuration must be specified." = "必须指定有效的配置。";

/* UTMAppleConfiguration */
"A valid kernel image must be specified." = "必须指定有效的内核映像。";

/* UTMScriptingAppDelegate */
"A valid UTM file must be specified." = "必须指定有效的 UTM 文件。";

/* VMDisplayAppleController */
"Add…" = "添加…";

/* No comment provided by engineer. */
"Additional Options" = "附加选项";

/* No comment provided by engineer. */
"Additional Settings" = "附加设置";

/* VMConfigSystemView */
"Allocating too much memory will crash the VM." = "分配过多内存会使虚拟机崩溃。";

/* UTMData */
"AltJIT error: %@" = "AltJIT 错误：%@";

/* UTMData */
"An existing virtual machine already exists with this name." = "已存在该名称的虚拟机。";

/* UTMConfiguration */
"An internal error has occurred." = "发生了内部错误。";

/* UTMConfiguration */
"An invalid value of '%@' is used in the configuration file." = "配置文件中使用了无效值“%@”。";

/* UTMRemoteSpiceVirtualMachine */
"An operation is already in progress." = "一项操作已经进行。";

/* UTMQemuImage */
"An unknown QEMU error has occurred." = "发生了未知的 QEMU 错误。";

/* VMDisplayAppleDisplayController */
"An USB device containing the installer will be mounted in the virtual machine. Only macOS Sequoia (15.0) and newer guests are supported." = "包含安装程序的 USB 设备将装载到虚拟机中。仅支持 macOS Sequoia (15.0) 及更新版本的客户机。";

/* No comment provided by engineer. */
"ANGLE (Metal)" = "ANGLE (Metal)";

/* No comment provided by engineer. */
"ANGLE (OpenGL)" = "ANGLE (OpenGL)";

/* VMConfigSystemView */
"Any unsaved changes will be lost." = "所有未存储的更改都将丢失。";

/* No comment provided by engineer. */
"Approve" = "批准";

/* No comment provided by engineer. */
"Architecture" = "架构";

/* No comment provided by engineer. */
"Are you sure you want to exit UTM?" = "你确定要退出 UTM 吗？";

/* No comment provided by engineer. */
"Are you sure you want to permanently delete this disk image?" = "你确定要永久删除此磁盘映像吗？";

/* No comment provided by engineer. */
"Are you sure you want to reset this VM? Any unsaved changes will be lost." = "你确定要重置此虚拟机吗？任何未存储的更改都将丢失。";

/* No comment provided by engineer. */
"Are you sure you want to stop this VM and exit? Any unsaved changes will be lost." = "你确定要停止此虚拟机并退出吗？任何未存储的更改都将丢失。";

/* No comment provided by engineer. */
"Authentication" = "认证";

/* No comment provided by engineer. */
"Automatic" = "自动";

/* UTMQemuConstants */
"Automatic Serial Device (max 4)" = "自动串行设备 (最大值 4)";

/* VMSessionState */
"Background task is about to expire" = "后台任务即将过期";

/* UTMLegacyQemuConfiguration
UTMQemuConstants */
"BIOS" = "BIOS";

/* No comment provided by engineer. */
"Block" = "屏蔽";

/* No comment provided by engineer. */
"Blocked" = "已屏蔽";

/* UTMQemuConstants */
"Bold" = "粗体";

/* No comment provided by engineer. */
"Boot" = "启动";

/* No comment provided by engineer. */
"Boot Arguments" = "启动参数";

/* No comment provided by engineer. */
"Boot Image Type" = "启动映像类型";

/* No comment provided by engineer. */
"Boot IMG Image" = "启动 IMG 映像";

/* No comment provided by engineer. */
"Boot ISO Image" = "启动 ISO 映像";

/* No comment provided by engineer. */
"Boot ISO Image (optional)" = "启动 ISO 映像 (可选)";

/* No comment provided by engineer. */
"Boot VHDX Image" = "启动 VHDX 映像";

/* UTMAppleConfigurationNetwork
UTMQemuConstants */
"Bridged (Advanced)" = "桥接 (高级)";

/* No comment provided by engineer. */
"Bridged Settings" = "桥接设置";

/* Welcome view */
"Browse UTM Gallery" = "浏览 UTM 库";

/* No comment provided by engineer. */
"Browse…" = "浏览…";

/* No comment provided by engineer. */
"Build" = "构建版本";

/* UTMAppleConfigurationTerminal
UTMQemuConstants */
"Built-in Terminal" = "内置终端";

/* No comment provided by engineer. */
"Busy…" = "正忙…";

/* VMDisplayWindowController
VMQemuDisplayMetalWindowController */
"Cancel" = "取消";

/* UTMAppleVirtualMachine */
"Cannot access resource: %@" = "无法访问资源：%@";

/* UTMSWTPM */
"Cannot access TPM data." = "无法访问 TPM 数据。";

/* UTMAppleVirtualMachine */
"Cannot create virtual terminal." = "无法创建虚拟终端。";

/* UTMData */
"Cannot find AltServer for JIT enable. You cannot run VMs until JIT is enabled." = "找不到用于 JIT 启用的 AltServer。在启用 JIT 之前，你将无法运行虚拟机。";

/* UTMRemoteServer */
"Cannot find VM with ID: %@" = "无法通过 ID 找到虚拟机：%@";

/* UTMData */
"Cannot import this VM. Either the configuration is invalid, created in a newer version of UTM, or on a platform that is incompatible with this version of UTM." = "无法导入此虚拟机。此虚拟机可能配置无效，或者是在较新版本的 UTM 中、与此版本的 UTM 不兼容的平台上创建。";

/* UTMRemoteServer */
"Cannot reserve port %d for external access from NAT. Make sure no other device on the network has reserved it." = "无法保留端口 %d 用作通过 NAT 的外部访问。确保网络上没有其他设备保留该端口。";

/* No comment provided by engineer. */
"Caps Lock (⇪) is treated as a key" = "将 Caps Lock (⇪) 视为按键处理";

/* VMMetalView */
"Capture Input" = "捕获输入";

/* No comment provided by engineer. */
"Capture input automatically when entering full screen" = "进入全屏幕时自动捕获输入";

/* No comment provided by engineer. */
"Capture input automatically when window is focused" = "窗口聚焦时自动捕获输入";

/* VMDisplayQemuMetalWindowController */
"Captured mouse" = "已捕获鼠标";

/* Configuration boot device */
"CD/DVD" = "CD/DVD";

/* UTMLegacyQemuConfiguration
UTMQemuConstants */
"CD/DVD (ISO) Image" = "CD/DVD (ISO) 映像";

/* No comment provided by engineer. */
"CD/DVD Image" = "CD/DVD 映像";

/* VMDisplayWindowController */
"Change" = "更改";

/* VMDisplayAppleController */
"Change…" = "更改…";

/* No comment provided by engineer. */
"Choose" = "选择";

/* No comment provided by engineer. */
"Clear" = "清除";

/* No comment provided by engineer. */
"Close" = "关闭";

/* VMQemuDisplayMetalWindowController */
"Closing this window will kill the VM." = "关闭此窗口将终止虚拟机。";

/* VMQemuDisplayMetalWindowController */
"Confirm" = "确认";

/* No comment provided by engineer. */
"Confirm Delete" = "确认删除";

/* AppDelegate
VMDisplayWindowController */
"Confirmation" = "确认";

/* No comment provided by engineer. */
"Connect" = "连接";

/* No comment provided by engineer. */
"Connected" = "已连接";

/* No comment provided by engineer. */
"Connection" = "连接";

/* VMSessionState */
"Connection to the server was lost." = "与服务器的连接已丢失。";

/* No comment provided by engineer. */
"Console" = "控制台";

/* No comment provided by engineer. */
"Continue" = "继续";

/* No comment provided by engineer. */
"CoreAudio (Output Only)" = "CoreAudio (仅限输出)";

/* No comment provided by engineer. */
"Cores" = "核心";

/* No comment provided by engineer. */
"CPU" = "CPU";

/* No comment provided by engineer. */
"CPU Cores" = "CPU 核心";

/* No comment provided by engineer. */
"Create" = "创建";

/* No comment provided by engineer. */
"Create a new emulated machine from scratch." = "从头开始创建一个新的虚拟机。";

/* Welcome view */
"Create a New Virtual Machine" = "创建一个新虚拟机";

/* No comment provided by engineer. */
"Create a new virtual machine or import an existing one." = "创建一个新虚拟机或导入现有的虚拟机。";

/* VMConfigAppleDisplayView */
"Custom" = "自定义";

/* UTMSWTPM */
"Data not specified." = "未指定数据。";

/* UTMDonateView */
"day" = "天";

/* No comment provided by engineer. */
"Debug Logging" = "调试日志记录";

/* QEMUConstantGenerated
UTMQemuConstants */
"Default" = "默认";

/* VMWizardSummaryView */
"Default Cores" = "默认核心数";

/* No comment provided by engineer. */
"Delete" = "删除";

/* No comment provided by engineer. */
"Devices" = "设备";

/* VMDisplayAppleWindowController */
"Directory sharing" = "目录共享";

/* UTMAppleConfigurationDevices
UTMQemuConstants */
"Disabled" = "已停用";

/* No comment provided by engineer. */
"Disconnect" = "断开连接";

/* No comment provided by engineer. */
"Discovered" = "已发现";

/* UTMLegacyQemuConfiguration
UTMQemuConstants */
"Disk Image" = "磁盘映像";

/* VMDisplayAppleWindowController */
"Display" = "显示";

/* VMDisplayQemuDisplayController */
"Display %lld: %@" = "显示 %1$lld：%2$@";

/* VMDisplayQemuDisplayController */
"Disposable Mode" = "一次性模式";

/* No comment provided by engineer. */
"Do not save VM screenshot to disk" = "不要将虚拟机截图存储到磁盘";

/* No comment provided by engineer. */
"Do not show confirmation when closing a running VM" = "关闭正在运行的虚拟机时不显示确认";

/* No comment provided by engineer. */
"Do not show prompt when USB device is plugged in" = "插入 USB 设备时不显示提示";

/* No comment provided by engineer. */
"Do you want to copy this VM and all its data to internal storage?" = "你要将此虚拟机及其所有数据拷贝到内部存储吗？";

/* No comment provided by engineer. */
"Do you want to delete this VM and all its data?" = "你要删除此虚拟机及其所有数据吗？";

/* No comment provided by engineer. */
"Do you want to download '%@'?" = "你要下载“%@”吗？";

/* No comment provided by engineer. */
"Do you want to duplicate this VM and all its data?" = "你要复制此虚拟机及其所有数据吗？";

/* No comment provided by engineer. */
"Do you want to force stop this VM and lose all unsaved data?" = "你要强制停止此虚拟机并丢失所有未存储的数据吗？";

/* No comment provided by engineer. */
"Do you want to forget all clients and generate a new server identity? Any clients that previously paired with this server will be instructed to manually unpair with this server before they can connect again." = "你要忽略所有客户端并生成新的服务器身份吗？之前与此服务器配对的任何客户端将被告知手动取消与此服务器的配对，之后才能再次连接。";

/* No comment provided by engineer. */
"Do you want to forget the selected client(s)?" = "你要忘记所选的客户端吗？";

/* No comment provided by engineer. */
"Do you want to move this VM to another location? This will copy the data to the new location, delete the data from the original location, and then create a shortcut." = "你要将此虚拟机移动到别处吗？这将会复制数据到新位置，删除原始位置的数据，然后创建一个快捷方式。";

/* No comment provided by engineer. */
"Do you want to remove this shortcut? The data will not be deleted." = "你要删除此快捷方式吗？数据不会被删除。";

/* No comment provided by engineer. */
"Download" = "下载";

/* No comment provided by engineer. */
"Download prebuilt from UTM Gallery…" = "从 UTM 库中下载预构建虚拟机…";

/* No comment provided by engineer. */
"Download VM" = "下载虚拟机";

/* No comment provided by engineer. */
"Drag and drop IPSW file here" = "拖放 IPSW 文件到此处";

/* UTMScriptingConfigImpl */
"Drive description is invalid." = "驱动器描述无效。";

/* No comment provided by engineer. */
"Drives" = "驱动器";

/* VMDrivesSettingsView */
"EFI Variables" = "EFI 变量";

/* VMDisplayWindowController */
"Eject" = "推出";

/* No comment provided by engineer. */
"Emulate" = "模拟";

/* UTMQemuConstants */
"Emulated VLAN" = "模拟 VLAN";

/* No comment provided by engineer. */
"Enable Clipboard Sharing" = "启用剪贴板共享";

/* No comment provided by engineer. */
"Enjoying the app? Consider making a donation to support development." = "喜欢这个 App 吗？考虑捐赠来支持开发吧！";

/* VMDisplayWindowController */
"Error" = "错误";

/* No comment provided by engineer. */
"Existing" = "现有的";

/* No comment provided by engineer. */
"Export QEMU Command…" = "导出 QEMU 命令…";

/* Word for decompressing a compressed folder */
"Extracting…" = "正在提取…";

/* UTMQemuVirtualMachine */
"Failed to access data from shortcut." = "无法从快捷方式访问数据。";

/* UTMQemuVirtualMachine */
"Failed to access drive image path." = "无法访问驱动器映像路径。";

/* UTMRemoteClient
UTMRemoteServer */
"Failed to access file." = "无法访问文件。";

/* UTMQemuVirtualMachine */
"Failed to access shared directory." = "无法访问共享目录。";

/* ContentView */
"Failed to attach to JitStreamer:\n%@" = "无法附加到 JitStreamer：%@";

/* UTMData */
"Failed to attach to JitStreamer." = "无法附加到 JitStreamer。";

/* UTMSpiceIO */
"Failed to change current directory." = "更改当前目录失败。";

/* UTMData */
"Failed to clone VM." = "复制虚拟机失败。";

/* UTMRemoteSpiceVirtualMachine */
"Failed to connect to SPICE: %@" = "无法连接到 SPICE：%@";

/* UTMPipeInterface */
"Failed to create pipe for communications." = "无法为通信创建管道。";

/* UTMData */
"Failed to decode JitStreamer response." = "无法解码 JitStreamer 响应。";

/* UTMRemoteClient */
"Failed to determine host name." = "无法确定主机名。";

/* UTMRemoteKeyManager */
"Failed to generate a key pair." = "无法生成密钥对。";

/* UTMQemuVirtualMachine */
"Failed to generate TLS key for server." = "无法为服务器生成 TLS 密钥。";

/* UTMRemoteClient */
"Failed to get host fingerprint." = "无法获取主机指纹。";

/* VMWizardState */
"Failed to get latest macOS version from Apple." = "无法从 Apple 获得最新的 macOS 版本。";

/* UTMRemoteKeyManager */
"Failed to import generated key." = "无法导入生成的密钥。";

/* UTMQemuConfigurationError */
"Failed to migrate configuration from a previous UTM version." = "无法从以前的 UTM 版本迁移配置。";

/* UTMRemoteKeyManager */
"Failed to parse generated key pair." = "无法解析生成的密钥对。";

/* UTMData */
"Failed to parse imported VM." = "无法解析导入的虚拟机。";

/* UTMDownloadVMTask */
"Failed to parse the downloaded VM." = "无法解析下载的虚拟机。";

/* UTMData */
"Failed to reconnect to the server." = "无法重新连接到服务器。";

/* AppDelegate
VMDisplayWindowController */
"Failed to save suspend state" = "无法存储挂起状态。";

/* UTMQemuVirtualMachine */
"Failed to save VM snapshot. Usually this means at least one device does not support snapshots. %@" = "无法存储虚拟机快照。通常这意味着至少有一个设备不支持快照。%@";

/* UTMSpiceIO */
"Failed to start SPICE client." = "无法启动 SPICE 客户端。";

/* No comment provided by engineer. */
"Faster, but can only run the native CPU architecture." = "速度更快，但只能运行本机 CPU 架构。";

/* No comment provided by engineer. */
"Fingerprint" = "指纹";

/* Configuration boot device
UTMQemuConstants */
"Floppy" = "软盘";

/* No comment provided by engineer. */
"Floppy Image" = "软盘映像";

/* No comment provided by engineer. */
"Font Size" = "字体大小";

/* VMDisplayWindowController */
"Force kill" = "强制终止";

/* VMDisplayWindowController */
"Force kill the VM process with high risk of data corruption." = "强制终止虚拟机进程 (会有高风险使数据损坏)。";

/* No comment provided by engineer. */
"Force Multicore" = "强制多核";

/* VMDisplayWindowController */
"Force shut down" = "强制关机";

/* UTMQemuConstants */
"GDB Debug Stub" = "GDB 调试存根";

/* No comment provided by engineer. */
"Generic" = "通用";

/* UTMAppleConfigurationDevices */
"Generic Mouse" = "通用鼠标";

/* UTMAppleConfigurationDevices */
"Generic USB" = "通用 USB";

/* No comment provided by engineer. */
"Gesture and Cursor Settings" = "手势和光标设置";

/* No comment provided by engineer. */
"GiB" = "GiB";

/* No comment provided by engineer. */
"Guest drivers are required for 3D acceleration." = "需要客户机驱动程序来使用 3D 加速。";

/* Configuration boot device */
"Hard Disk" = "硬盘";

/* No comment provided by engineer. */
"Hardware" = "硬件";

/* No comment provided by engineer. */
"Hello" = "你好";

/* No comment provided by engineer. */
"Hide Unused…" = "隐藏未使用的…";

/* No comment provided by engineer. */
"Hold Control (⌃) for right click" = "按住 Control (⌃) 键以右键单击";

/* No comment provided by engineer. */
"Host" = "主机";

/* UTMQemuConstants */
"Host Only" = "仅主机";

/* No comment provided by engineer. */
"Hostname or IP address" = "主机名或 IP 地址";

/* No comment provided by engineer. */
"Icon" = "图标";

/* UTMQemuConstants */
"IDE" = "IDE";

/* UTMScriptingConfigImpl */
"Identifier '%@' cannot be found." = "找不到标识符 '%@'。";

/* No comment provided by engineer. */
"Image File Type" = "映像文件类型";

/* No comment provided by engineer. */
"Import Drive…" = "导入驱动器…";

/* No comment provided by engineer. */
"Import IPSW" = "导入 IPSW";

/* No comment provided by engineer. */
"Import…" = "导入…";

/* VMDetailsView */
"Inactive" = "非活跃";

/* UTMScriptingConfigImpl */
"Index %lld cannot be found." = "找不到索引 %lld。";

/* No comment provided by engineer. */
"Information" = "信息";

/* VMDisplayAppleWindowController */
"Install Guest Tools…" = "安装客户机工具…";

/* VMDisplayWindowController */
"Install Windows Guest Tools…" = "安装 Windows 客户机工具…";

/* VMDisplayAppleWindowController */
"Installation: %@" = "安装：%@";

/* UTMProcess */
"Internal error has occurred." = "发生内部错误。";

/* UTMSpiceIO */
"Internal error trying to connect to SPICE server." = "尝试连接到 SPICE 服务器时出现内部错误。";

/* VMDisplayMetalWindowController */
"Internal error." = "内部错误。";

/* UTMRemoteServer */
"Invalid backend." = "无效的后端。";

/* VMWizardState */
"Invalid drive size specified." = "指定的驱动器大小无效。";

/* UTMData */
"Invalid JitStreamer attach URL:\n%@" = "无效的 JitStreamer 附加 URL：%@";

/* VMConfigAppleNetworkingView */
"Invalid MAC address." = "MAC 地址无效。";

/* VMWizardState */
"Invalid RAM size specified." = "指定的内存大小无效。";

/* No comment provided by engineer. */
"Invert scrolling" = "反转滚动";

/* No comment provided by engineer. */
"IP Configuration" = "IP 配置";

/* No comment provided by engineer. */
"Isolate Guest from Host" = "将客户机与主机隔离";

/* UTMQemuConstants */
"Italic" = "斜体";

/* UTMQemuConstants */
"Italic, Bold" = "斜体，粗体";

/* No comment provided by engineer. */
"Keep UTM running after last window is closed and all VMs are shut down" = "在关闭最后一个窗口和所有虚拟机关机后继续运行 UTM";

/* No comment provided by engineer. */
"License" = "许可";

/* UTMQemuConstants */
"Linear" = "线性";

/* UTMAppleConfigurationBoot */
"Linux" = "Linux";

/* UTMLegacyQemuConfiguration
UTMQemuConstants */
"Linux Device Tree Binary" = "Linux 设备树二进制文件";

/* No comment provided by engineer. */
"Linux initial ramdisk (optional)" = "Linux 初始 ramdisk (可选)";

/* UTMLegacyQemuConfiguration
UTMQemuConstants */
"Linux Kernel" = "Linux 内核";

/* No comment provided by engineer. */
"Linux kernel (required)" = "Linux 内核 (必选)";

/* UTMLegacyQemuConfiguration
UTMQemuConstants */
"Linux RAM Disk" = "Linux ramdisk";

/* No comment provided by engineer. */
"Linux Root FS Image (optional)" = "Linux rootfs 映像 (可选)";

/* No comment provided by engineer. */
"Linux Settings" = "Linux 设置";

/* No comment provided by engineer. */
"Logging" = "日志";

/* UTMAppleConfigurationDevices */
"Mac Keyboard (macOS 14+)" = "Mac 键盘 (macOS 14+)";

/* UTMAppleConfigurationDevices */
"Mac Trackpad (macOS 13+)" = "Mac 触控板 (macOS 14+)";

/* UTMAppleConfigurationBoot */
"macOS" = "macOS";

/* UTMDownloadMacSupportToolsTask */
"macOS Guest Support Tools" = "macOS 客户机支持工具";

/* VMWizardOSMacView */
"macOS guests are only supported on ARM64 devices." = "macOS 客户机仅支持 ARM64 设备。";

/* VMWizardState */
"macOS is not supported with QEMU." = "QEMU 不支持 macOS。";

/* No comment provided by engineer. */
"macOS Settings" = "macOS 设置";

/* No comment provided by engineer. */
"Make sure the latest version of UTM is running on your Mac and UTM Server is enabled. You can download UTM from the Mac App Store." = "确保你的 Mac 上运行最新版本的 UTM，且 UTM 服务器已启用。你可以从 Mac App Store 下载 UTM。";

/* UTMQemuConstants */
"Manual Serial Device (advanced)" = "手动串行设备 (高级)";

/* No comment provided by engineer. */
"Maximum Shared USB Devices" = "最大共享 USB 设备数";

/* No comment provided by engineer. */
"Memory" = "内存";

/* VMDisplayMetalWindowController */
"Metal is not supported on this device. Cannot render display." = "此设备不支持 Metal。无法渲染显示。";

/* No comment provided by engineer. */
"MiB" = "MiB";

/* No comment provided by engineer. */
"Minimum size: %@" = "最小文件大小：%@";

/* UTMDonateView */
"month" = "月";

/* No comment provided by engineer. */
"Mouse/Keyboard" = "鼠标/键盘";

/* No comment provided by engineer. */
"Move Down" = "下移";

/* No comment provided by engineer. */
"Move Up" = "上移";

/* UTMQemuConstants */
"MTD (NAND/NOR)" = "MTD (NAND/NOR)";

/* No comment provided by engineer. */
"Name" = "名称";

/* No comment provided by engineer. */
"Name (optional)" = "名称 (可选)";

/* UTMQemuConstants */
"Nearest Neighbor" = "近邻取样";

/* No comment provided by engineer. */
"Network" = "网络";

/* No comment provided by engineer. */
"New" = "新建";

/* No comment provided by engineer. */
"New Drive…" = "新建驱动器…";

/* No comment provided by engineer. */
"New Machine" = "新建虚拟机";

/* No comment provided by engineer. */
"New…" = "新建…";

/* No comment provided by engineer. */
"No" = "否";

/* UTMScriptingAppDelegate */
"No architecture specified in the configuration." = "配置中未指定架构。";

/* VMDisplayWindowController */
"No drives connected." = "没有连接的驱动器。";

/* UTMDownloadSupportToolsTaskError */
"No empty removable drive found. Make sure you have at least one removable drive that is not in use." = "未找到空的可移动驱动器。确保你至少有一个未使用的可移动驱动器。";

/* UTMScriptingAppDelegate */
"No file specified in the command." = "命令中未指定文件。";

/* UTMScriptingAppDelegate */
"No name specified in the configuration." = "配置中未指定名称。";

/* No comment provided by engineer. */
"No output device is selected for this window." = "此窗口未选择输出设备。";

/* No comment provided by engineer. */
"No release notes found for version %@." = "未找到 %@ 版本的发布说明。";

/* VMQemuDisplayMetalWindowController */
"No USB devices detected." = "未检测到 USB 设备。";

/* No comment provided by engineer. */
"No virtual machines found." = "未找到虚拟机。";

/* VMDisplayAppleDisplayController
VMDisplayQemuDisplayController
VMToolbarDriveMenuView */
"none" = "无";

/* UTMAppleConfigurationBoot
UTMLegacyQemuConfiguration
UTMQemuConstants */
"None" = "无";

/* UTMQemuConstants */
"None (Advanced)" = "无 (高级)";

/* UTMRemoteServer */
"Not authenticated." = "未认证。";

/* UTMVirtualMachine */
"Not implemented." = "未实现。";

/* No comment provided by engineer. */
"Notes" = "注释";

/* No comment provided by engineer. */
"Num Lock is forced on" = "强制打开数字锁定 (Num Lock)";

/* UTMQemuConstants */
"NVMe" = "NVMe";

/* VMDisplayWindowController */
"OK" = "好";

/* UTMScriptingVirtualMachineImpl */
"One or more required parameters are missing or invalid." = "一个或多个必填的参数缺失或无效。";

/* No comment provided by engineer. */
"Open…" = "打开…";

/* No comment provided by engineer. */
"Operating System" = "操作系统";

/* UTMScriptingVirtualMachineImpl */
"Operation not available." = "操作不可用。";

/* UTMData
UTMScriptingVirtualMachineImpl */
"Operation not supported by the backend." = "操作不受后端支持。";

/* No comment provided by engineer. */
"Option (⌥) is Meta key" = "将 Option (⌥) 键视为 Meta 键";

/* No comment provided by engineer. */
"Options" = "选项";

/* No comment provided by engineer. */
"Other" = "其他";

/* No comment provided by engineer. */
"Password" = "密码";

/* UTMRemoteClient */
"Password is incorrect." = "密码不正确。";

/* UTMRemoteClient */
"Password is required." = "需要密码。";

/* VMDisplayWindowController */
"Pause" = "暂停";

/* VMData */
"Paused" = "已暂停";

/* VMData */
"Pausing" = "正在暂停";

/* UTMQemuConstants */
"PC System Flash" = "PC 系统闪存";

/* No comment provided by engineer. */
"Pending" = "待处理";

/* UTMDonateView */
"period" = "周期";

/* VMDisplayWindowController */
"Play" = "启动";

/* VMWizardState */
"Please select a boot image." = "请选择一个启动映像。";

/* VMWizardState */
"Please select a kernel file." = "请选择一个内核文件。";

/* No comment provided by engineer. */
"Please select a macOS recovery IPSW." = "请选择一个 macOS IPSW 恢复文件。";

/* No comment provided by engineer. */
"Please select an uncompressed Linux kernel image." = "请选择一个未压缩的 Linux 内核映像。";

/* No comment provided by engineer. */
"Port" = "端口";

/* No comment provided by engineer. */
"Port Forward" = "端口转发";

/* No comment provided by engineer. */
"Preconfigured" = "预配置";

/* A download process is about to begin. */
"Preparing…" = "正在准备…";

/* VMDisplayQemuMetalWindowController */
"Press %@ to release cursor" = "按下 %@ 来释放光标";

/* No comment provided by engineer. */
"Prevent system from sleeping when any VM is running" = "当任何虚拟机运行时防止系统处于睡眠状态";

/* UTMAppleConfigurationTerminal
UTMQemuConstants */
"Pseudo-TTY Device" = "虚拟终端设备";

/* No comment provided by engineer. */
"QEMU Arguments" = "QEMU 参数";

/* No comment provided by engineer. */
"QEMU Graphics Acceleration" = "QEMU 图形加速";

/* No comment provided by engineer. */
"QEMU Keyboard" = "QEMU 键盘";

/* UTMQemuConstants */
"QEMU Monitor (HMP)" = "QEMU 监视器 (HMP)";

/* No comment provided by engineer. */
"QEMU Pointer" = "QEMU 指针";

/* No comment provided by engineer. */
"QEMU Sound" = "QEMU 声音";

/* No comment provided by engineer. */
"QEMU USB" = "QEMU USB";

/* VMDisplayWindowController */
"Querying drives status..." = "正在查询驱动器状态…";

/* VMQemuDisplayMetalWindowController */
"Querying USB devices..." = "正在查询 USB 设备状态…";

/* VMQemuDisplayMetalWindowController */
"Quitting UTM will kill all running VMs." = "退出 UTM 将终止所有正在运行的虚拟机。";

/* No comment provided by engineer. */
"Raw Image" = "raw 映像";

/* VMDisplayAppleController */
"Read Only" = "只读";

/* No comment provided by engineer. */
"Reclaim" = "回收";

/* UTMQemuConstants */
"Regular" = "常规";

/* VMRemovableDrivesView */
"Removable" = "可移除";

/* No comment provided by engineer. */
"Removable Drive" = "可移除驱动器";

/* No comment provided by engineer. */
"Remove" = "移除";

/* VMDisplayAppleController */
"Remove…" = "移除…";

/* VMDisplayWindowController */
"Request power down" = "请求关闭电源";

/* No comment provided by engineer. */
"Reset" = "重置";

/* No comment provided by engineer. */
"Reset Identity" = "重置身份";

/* No comment provided by engineer. */
"Resize" = "重新调整";

/* No comment provided by engineer. */
"Resize display to screen size and orientation automatically" = "自动将显示调整为屏幕大小和方向";

/* No comment provided by engineer. */
"Resize display to window size automatically" = "自动将显示调整为窗口大小";

/* No comment provided by engineer. */
"Resizing is experimental and could result in data loss. You are strongly encouraged to back-up this VM before proceeding. Would you like to resize to %@ GiB?" = "调整驱动器大小是实验性功能，可能会导致数据丢失。在继续操作之前，强烈建议你备份此虚拟机。你要将大小调整为 %@ GiB 吗？";

/* VMData */
"Restoring" = "正在恢复";

/* VMData */
"Resuming" = "正在恢复";

/* No comment provided by engineer. */
"Retina Mode" = "Retina 模式";

/* UTMAppleConfiguration */
"Rosetta is not supported on the current host machine." = "当前主机不支持 Rosetta。";

/* No comment provided by engineer. */
"Running" = "正在运行";

/* No comment provided by engineer. */
"Running low on memory! UTM might soon be killed by iOS. You can prevent this by decreasing the amount of memory and/or JIT cache assigned to this VM" = "运行内存不足！UTM 可能很快就会被 iOS 终止。你可以通过减少分配给此虚拟机的内存和/或 JIT 缓存来防止这种情况。";

/* No comment provided by engineer. */
"Save" = "存储";

/* No comment provided by engineer. */
"Saved" = "已存储";

/* VMData */
"Saving" = "正在存储";

/* No comment provided by engineer. */
"Scaling" = "粗化";

/* UTMQemuConstants */
"SCSI" = "SCSI";

/* UTMQemuConstants */
"SD Card" = "SD 卡";

/* No comment provided by engineer. */
"Select a file." = "选择一个文件。";

/* No comment provided by engineer. */
"Select a UTM Server" = "选择一个 UTM 服务器";

/* VMDisplayWindowController */
"Select Drive Image" = "选择驱动器映像";

/* VMDisplayAppleWindowController
VMDisplayWindowController */
"Select Shared Folder" = "选择共享的文件夹";

/* SavePanel */
"Select where to export QEMU command:" = "选择导出 QEMU 命令的位置：";

/* SavePanel */
"Select where to save debug log:" = "选择存储调试日志的位置：";

/* SavePanel */
"Select where to save UTM Virtual Machine:" = "选择存储 UTM 虚拟机的位置：";

/* No comment provided by engineer. */
"Selected:" = "已选择：";

/* VMDisplayWindowController */
"Sends power down request to the guest. This simulates pressing the power button on a PC." = "向客户机发送关闭电源请求。此操作模拟了按下 PC 上的电源按钮。";

/* VMDisplayAppleWindowController
VMDisplayQemuDisplayController */
"Serial %lld" = "串行端口 %lld";

/* Server view */
"Server" = "服务器";

/* No comment provided by engineer. */
"Server IP: %@, Port: %@" = "服务器 IP：%1$@，端口：%2$@";

/* No comment provided by engineer. */
"Share USB devices from host" = "从主机共享 USB 设备";

/* No comment provided by engineer. */
"Shared directories in macOS VMs are only available in macOS 13 and later." = "macOS 虚拟机中的共享目录仅在 macOS 13 及更高版本中可用。";

/* No comment provided by engineer. */
"Shared Directory" = "共享目录";

/* UTMAppleConfigurationNetwork
UTMQemuConstants */
"Shared Network" = "共享网络";

/* No comment provided by engineer. */
"Sharing" = "共享";

/* No comment provided by engineer. */
"Show Advanced Settings" = "显示高级设置";

/* No comment provided by engineer. */
"Show All" = "显示全部";

/* No comment provided by engineer. */
"Show All…" = "显示全部…";

/* No comment provided by engineer. */
"Show dock icon" = "显示程序坞图标";

/* No comment provided by engineer. */
"Show menu bar icon" = "显示菜单栏图标";

/* No comment provided by engineer. */
"Size" = "大小";

/* No comment provided by engineer. */
"Slower, but can run other CPU architectures." = "速度较慢，但可以运行其他 CPU 架构。";

/* UTMSWTPM */
"Socket not specified." = "未指定套接字。";

/* No comment provided by engineer. */
"Specify the size of the drive where data will be stored into." = "指定将在其中存储数据的驱动器大小。";

/* UTMQemuConstants */
"SPICE WebDAV" = "SPICE WebDAV";

/* No comment provided by engineer. */
"SPICE with GStreamer (Input & Output)" = "带有 GStreamer 的 SPICE (输入与输出)";

/* No comment provided by engineer. */
"Start Here" = "从这里开始";

/* VMData */
"Started" = "已启动";

/* VMData */
"Starting" = "正在启动";

/* No comment provided by engineer. */
"Startup" = "启动";

/* No comment provided by engineer. */
"Stop" = "停止";

/* VMData */
"Stopped" = "已停止";

/* VMData */
"Stopping" = "正在停止";

/* No comment provided by engineer. */
"Style" = "样式";

/* No comment provided by engineer. */
"Summary" = "总结";

/* Welcome view */
"Support" = "支持";

/* No comment provided by engineer. */
"Support UTM" = "支持 UTM";

/* UTMQemuVirtualMachine */
"Suspend is not supported for virtualization." = "挂起功能不支持虚拟化。";

/* UTMQemuVirtualMachine */
"Suspend is not supported when an emulated NVMe device is active." = "当模拟的 NVMe 设备处于活动状态时不支持挂起。";

/* UTMQemuVirtualMachine */
"Suspend is not supported when GPU acceleration is enabled." = "启用 GPU 加速时不支持挂起。";

/* UTMQemuVirtualMachine */
"Suspend state cannot be saved when running in disposible mode." = "在一次性模式下运行时无法存储挂起状态。";

/* VMData */
"Suspended" = "已挂起";

/* UTMSWTPM */
"SW TPM failed to start. %@" = "SW TPM 无法启动。%@";

/* VMSessionState */
"Switch back to UTM to avoid termination." = "切换回 UTM 以避免终止。";

/* No comment provided by engineer. */
"System" = "系统";

/* No comment provided by engineer. */
"Tap to hide/show toolbar" = "点按以隐藏/显示工具栏";

/* UTMQemuConstants */
"TCP" = "TCP";

/* UTMQemuConstants */
"TCP Client Connection" = "TCP 客户端连接";

/* UTMQemuConstants */
"TCP Server Connection" = "TCP 服务器连接";

/* VMDisplayWindowController */
"Tells the VM process to shut down with risk of data corruption. This simulates holding down the power button on a PC." = "通知虚拟机进程关闭 (存在数据损坏的风险)。这一操作模拟了按住 PC 上的电源按钮。";

/* No comment provided by engineer. */
"Test" = "测试";

/* No comment provided by engineer. */
"Test 1" = "测试 1";

/* No comment provided by engineer. */
"Test 2" = "测试 2";

/* UTMConfiguration */
"The backend for this configuration is not supported." = "不支持此配置的后端。";

/* UTMRemoteServer */
"The client interface version does not match the server." = "客户端接口版本与服务器不匹配。";

/* UTMScriptingUSBDeviceImpl */
"The device cannot be found." = "找不到该设备。";

/* UTMScriptingUSBDeviceImpl */
"The device is not currently connected." = "设备目前尚未连接。";

/* UTMConfiguration */
"The drive '%@' already exists and cannot be created." = "驱动器“%@”已存在，无法创建。";

/* UTMDownloadSupportToolsTaskError */
"The guest support tools have already been mounted." = "客户机支持工具已装载。";

/* UTMRemoteClient */
"The host fingerprint does not match the saved value. This means that UTM Server was reset, a different host is using the same name, or an attacker is pretending to be the host. For your protection, you need to delete this saved host to continue." = "主机指纹与存储的值不匹配。这意味着 UTM 服务器被重置、不同的主机使用相同的名称，或者攻击者正在冒充主机。为了保护你的安全，你需要删除已存储的主机才能继续。";

/* UTMAppleConfiguration */
"The host operating system needs to be updated to support one or more features requested by the guest." = "需要更新主机操作系统以支持客户机请求的一个或多个功能。";

/* UTMAppleVirtualMachine */
"The operating system cannot be installed on this machine." = "操作系统无法安装在此机器上。";

/* UTMAppleVirtualMachine */
"The operation is not available." = "此操作不可用。";

/* UTMScriptingVirtualMachineImpl */
"The QEMU guest agent is not running or not installed on the guest." = "QEMU 客户机代理没有运行或未安装在客户机上。";

/* No comment provided by engineer. */
"The selected architecture is unsupported in this version of UTM." = "此版本的 UTM 不支持所选架构。";

/* VMWizardState */
"The selected boot image contains the word '%@' but the guest architecture is '%@'. Please ensure you have selected an image that is compatible with '%@'." = "所选的启动映像名称包含“%@”，但客户机的架构为“%@”。请确保你选择了与“%@”兼容的映像。";

/* UTMRemoteClient */
"The server interface version does not match the client." = "服务器接口版本与客户端不匹配。";

/* No comment provided by engineer. */
"The target does not support hardware emulated serial connections." = "目标系统不支持硬件模拟串行连接。";

/* UTMQemuVirtualMachine */
"The virtual machine is in an invalid state." = "虚拟机处于无效状态。";

/* UTMScriptingVirtualMachineImpl */
"The virtual machine is not running." = "虚拟机未运行。";

/* UTMScriptingVirtualMachineImpl */
"The virtual machine must be stopped before this operation can be performed." = "在执行此操作之前，必须停止虚拟机。";

/* Error shown when importing a ZIP file from web that doesn't contain a UTM Virtual Machine. */
"There is no UTM file in the downloaded ZIP archive." = "下载的 ZIP 归档中无 UTM 文件。";

/* No comment provided by engineer. */
"This audio card is not supported." = "此声卡不受支持。";

/* UTMScriptingAppDelegate */
"This backend is not supported on your machine." = "你的机器不支持此后端。";

/* No comment provided by engineer. */
"This build does not emulation." = "此版本不支持模拟。";

/* UTMQemuVirtualMachine */
"This build of UTM does not support emulating the architecture of this VM." = "此 UTM 版本不支持模拟此虚拟机的架构。";

/* VMConfigSystemView */
"This change will reset all settings" = "此更改将重置所有设置";

/* UTMConfiguration */
"This configuration is saved with a newer version of UTM and is not compatible with this version." = "此配置是用较新版本的 UTM 存储的，并且与此版本不兼容。";

/* UTMConfiguration */
"This configuration is too old and is not supported." = "此配置过旧，无法支持。";

/* UTMScriptingConfigImpl */
"This device is not supported by the target." = "目标不支持此设备。";

/* VMConfigAppleSharingView */
"This directory is already being shared." = "此目录已被共享。";

/* VMData */
"This function is not implemented." = "此功能未实现。";

/* UTMData */
"This functionality is not yet implemented." = "此功能尚未实现。";

/* UTMRemoteClient */
"This host is not yet trusted. You should verify that the fingerprints match what is displayed on the host and then select Trust to continue." = "此主机尚未被信任。你应该验证指纹是否与主机上所显示的匹配，然后选择“信任”以继续。";

/* UTMAppleConfiguration */
"This is not a valid Apple Virtualization configuration." = "并非有效的 Apple 虚拟化配置。";

/* VMDisplayWindowController */
"This may corrupt the VM and any unsaved changes will be lost. To quit safely, shut down from the guest." = "这可能会损坏虚拟机，任何未存储的更改都将丢失。为了安全退出，请从客户机操作系统关闭。";

/* No comment provided by engineer. */
"This operating system is unsupported on your machine." = "你的机器不支持此操作系统。";

/* UTMDataExtension */
"This virtual machine cannot be run on this machine." = "此虚拟机无法在这台机器上运行。";

/* UTMAppleConfiguration */
"This virtual machine cannot run on the current host machine." = "此虚拟机无法在当前主机上运行。";

/* UTMAppleConfiguration */
"This virtual machine contains an invalid hardware model. The configuration may be corrupted or is outdated." = "此虚拟机包含无效的硬件型号，其配置可能已损坏或过时。";

/* No comment provided by engineer. */
"This virtual machine has been removed." = "此虚拟机已被移除。";

/* UTMDataExtension */
"This virtual machine is already running. In order to run it from this device, you must stop it first." = "此虚拟机已在运行。若要从该设备运行此虚拟机，你必须先停止它。";

/* UTMData */
"This virtual machine is currently unavailable, make sure it is not open in another session." = "此虚拟机当前不可用，确保它没有在另一个会话中打开。";

/* VMData */
"This VM is not available or is configured for a backend that does not support remote clients." = "此虚拟机不可用，或配置为不支持远程客户端的后端。";

/* No comment provided by engineer. */
"This VM is unavailable." = "此虚拟机不可用。";

/* VMDisplayWindowController */
"This will reset the VM and any unsaved state will be lost." = "这将重置虚拟机，任何未存储的状态都将丢失。";

/* UTMRemoteConnectView */
"Timed out trying to connect." = "尝试连接超时。";

/* VMDisplayAppleWindowController */
"To access the shared directory, the guest OS must have Virtiofs drivers installed. You can then run `sudo mount -t virtiofs share /path/to/share` to mount to the share path." = "若要访问共享目录，客户机操作系统必须安装 VirtioFS 驱动程序。然后，你可以运行命令 `sudo mount -t virtiofs share [要共享的目录]` 来装载到共享路径。";

/* VMMetalView */
"To capture input or to release the capture, press Command and Option at the same time." = "若要捕获或释放捕获输入，请同时按下 Command 和 Option 键。";

/* No comment provided by engineer. */
"To install macOS, you need to download a recovery IPSW. If you do not select an existing IPSW, the latest macOS IPSW will be downloaded from Apple." = "若要安装 macOS，你需要下载 IPSW 恢复文件。如果你没有选择现有的 IPSW，将会从 Apple 下载最新的 macOS IPSW。";

/* VMDisplayQemuMetalWindowController */
"To release the mouse cursor, press %@ at the same time." = "若要释放鼠标光标，请同时按 %@。";

/* No comment provided by engineer. */
"Trust" = "信任";

/* No comment provided by engineer. */
"u{2022} " = "u{2022}";

/* UTMQemuConstants */
"UDP" = "UDP";

/* No comment provided by engineer. */
"UEFI" = "UEFI";

/* UTMQemuConfigurationError */
"UEFI is not supported with this architecture." = "此架构不支持 UEFI。";

/* UTMData */
"Unable to add a shortcut to the new location." = "无法向新位置添加快捷方式。";

/* VMData */
"Unavailable" = "不可用";

/* VMWizardState */
"Unavailable for this platform." = "此平台不可用。";

/* No comment provided by engineer. */
"Uncompressed Linux initial ramdisk (optional)" = "未压缩的 Linux 初始 ramdisk (可选)";

/* No comment provided by engineer. */
"Uncompressed Linux kernel (required)" = "未压缩的 Linux 内核文件 (必选)";

/* No comment provided by engineer. */
"Update Interface" = "更新界面";

/* UTMQemuConstants */
"USB" = "USB";

/* UTMQemuConstants */
"USB 2.0" = "USB 2.0";

/* UTMQemuConstants */
"USB 3.0 (XHCI)" = "USB 3.0 (XHCI)";

/* VMQemuDisplayMetalWindowController */
"USB Device" = "USB 设备";

/* VMDisplayAppleDisplayController */
"USB Mass Storage: %@" = "USB 海量储存设备：%@";

/* No comment provided by engineer. */
"USB Sharing" = "USB 共享";

/* No comment provided by engineer. */
"USB sharing not supported in this build of UTM." = "此版本的 UTM 不支持 USB 共享。";

/* No comment provided by engineer. */
"Use Command+Option (⌘+⌥) for input capture/release" = "使用 Command + Option (⌘ + ⌥) 捕获/释放输入";

/* No comment provided by engineer. */
"Use NVMe Interface" = "使用 NVMe 磁盘接口";

/* Welcome view */
"User Guide" = "用户指南";

/* UTMScriptingAppDelegate
UTMScriptingUSBDeviceImpl */
"UTM is not ready to accept commands." = "UTM 尚未准备好接受命令。";

/* No comment provided by engineer. */
"Version" = "版本号";

/* UTMQemuConstants */
"VirtFS" = "VirtFS";

/* UTMQemuConstants */
"VirtIO" = "VirtIO";

/* UTMConfigurationInfo
UTMData
VMMetalView */
"Virtual Machine" = "虚拟机";

/* No comment provided by engineer. */
"Virtual Machine Gallery" = "虚拟机库";

/* VMData */
"Virtual machine not loaded." = "未加载虚拟机。";

/* No comment provided by engineer. */
"Virtualization is not supported on your system." = "你的系统不支持虚拟化。";

/* No comment provided by engineer. */
"Virtualize" = "虚拟化";

/* No comment provided by engineer. */
"Waiting for VM to connect to display..." = "等待虚拟机连接到显示…";

/* UTMDonateView */
"week" = "周";

/* No comment provided by engineer. */
"Welcome to UTM" = "欢迎使用 UTM";

/* No comment provided by engineer. */
"What's New" = "新版特性";

/* No comment provided by engineer. */
"When the toolbar is hidden, the icon will disappear after a few seconds. To show the icon again, tap anywhere on the screen." = "当工具栏被隐藏时，图标将会在几秒钟后消失。若要再次显示图标，请点按屏幕上的任意位置。";

/* UTMDownloadSupportToolsTask */
"Windows Guest Support Tools" = "Windows 客户机支持工具";

/* VMQemuDisplayMetalWindowController */
"Would you like to connect '%@' to this virtual machine?" = "你要将 '%@' 连接到此虚拟机吗？";

/* VMDisplayAppleWindowController */
"Would you like to install macOS? If an existing operating system is already installed on the primary drive of this VM, then it will be erased." = "你要安装 macOS 吗？若现有的操作系统已安装在该虚拟机的主驱动器上，则它将被抹掉。";

/* No comment provided by engineer. */
"Would you like to re-convert this disk image to reclaim unused space and apply compression? Note this will require enough temporary space to perform the conversion. Compression only applies to existing data and new data will still be written uncompressed. You are strongly encouraged to back-up this VM before proceeding." = "你要重新转换此磁盘映像以回收未使用的空间并压缩吗？请注意，这将需要足够的临时空间来执行转换。此压缩过程仅适用于现有数据，新数据仍将以未压缩形式写入。在继续操作之前，强烈建议你备份此虚拟机。";

/* No comment provided by engineer. */
"Would you like to re-convert this disk image to reclaim unused space? Note this will require enough temporary space to perform the conversion. You are strongly encouraged to back-up this VM before proceeding." = "你要重新转换此磁盘映像以回收未使用的空间吗？请注意，这将需要足够的临时空间来执行转换。在继续操作之前，强烈建议你备份此虚拟机。";

/* UTMDonateView */
"year" = "年";

/* No comment provided by engineer. */
"Yes" = "是";

/* VMConfigSystemView */
"Your device has %llu MB of memory and the estimated usage is %llu MB." = "你的设备有 %llu MB 的内存，估计使用量为 %llu MB。";

/* VMConfigAppleBootView
VMWizardOSMacView */
"Your machine does not support running this IPSW." = "你的机器不支持运行此 IPSW。";

/* UTMDonateView */
"Your purchase could not be verified by the App Store." = "App Store 无法验证你的购买。";

/* No comment provided by engineer. */
"Your support is the driving force that helps UTM stay independent. Your contribution, no matter the size, makes a significant difference. It enables us to develop new features and maintain existing ones. Thank you for considering a donation to support us." = "你的支持是 UTM 保持独立的动力。你的贡献，无论多少，都会产生重大影响。这可以让我们开发功能，并维护现有的功能。感谢你考虑捐赠支持我们。";

/* ContentView */
"Your version of iOS does not support running VMs while unmodified. You must either run UTM while jailbroken or with a remote debugger attached. See https://getutm.app/install/ for more details." = "你的 iOS 版本不支持在未经修改的情况下运行虚拟机，必须在越狱时运行 UTM，或者附加远程调试器。有关更多详细信息，请参阅 https://getutm.app/install/。";

// Additional Strings (These strings are unable to be extracted by Xcode)

/* No comment provided by engineer. */
"" = "";

/* No comment provided by engineer. */
"(Delete)" = "(删除)";

/* No comment provided by engineer. */
"Add" = "添加";

/* No comment provided by engineer. */
"Add a new device." = "添加一个新设备。";

/* No comment provided by engineer. */
"Add a new drive." = "添加一个新驱动器。";

/* No comment provided by engineer. */
"Add read only" = "添加只读";

/* No comment provided by engineer. */
"Advanced" = "高级";

/* No comment provided by engineer. */
"Advanced. If checked, a raw disk image is used. Raw disk image does not support snapshots and will not dynamically expand in size." = "高级选项。若选中，将使用 raw 磁盘映像。raw 磁盘映像既不支持快照，也不会动态地扩充大小。";

/* No comment provided by engineer. */
"Allow access from external clients" = "允许外部客户机访问";

/* No comment provided by engineer. */
"Allow Remote Connection" = "允许远程连接";

/* No comment provided by engineer. */
"Allows passing through additional input from trackpads. Only supported on macOS 13+ guests." = "允许通过触控板额外输入。仅支持 macOS 13 及以上的客户机。";

/* No comment provided by engineer. */
"Any" = "任意";

/* No comment provided by engineer. */
"Apple Virtualization is experimental and only for advanced use cases. Leave unchecked to use QEMU, which is recommended." = "Apple 虚拟化属于实验性功能，仅适用于高级用例。推荐不选中此复选框，以便使用 QEMU。";

/* No comment provided by engineer. */
"Application" = "应用程序";

/* No comment provided by engineer. */
"Architecture" = "架构";

/* No comment provided by engineer. */
"Arguments" = "参数";

/* No comment provided by engineer. */
"Auto Resolution" = "自动调整分辨率";

/* No comment provided by engineer. */
"Automatic" = "自动";

/* No comment provided by engineer. */
"Automatically start UTM server" = "自动启动 UTM 服务器";

/* No comment provided by engineer. */
"Background Color" = "背景颜色";

/* No comment provided by engineer. */
"Balloon Device" = "Balloon 设备";

/* No comment provided by engineer. */
"Blinking cursor?" = "闪烁光标？";

/* No comment provided by engineer. */
"Boot arguments" = "启动参数";

/* No comment provided by engineer. */
"Boot Arguments" = "启动参数";

/* No comment provided by engineer. */
"Boot Device" = "启动设备";

/* No comment provided by engineer. */
"Boot from kernel image" = "从内核映像启动";

/* No comment provided by engineer. */
"Boot Image" = "启动映像";

/* No comment provided by engineer. */
"Boot Image Type" = "启动映像类型";

/* No comment provided by engineer. */
"Boot into recovery mode." = "启动到还原模式。";

/* No comment provided by engineer. */
"Bootloader" = "Bootloader";

/* No comment provided by engineer. */
"Bridged Interface" = "桥接接口";

/* No comment provided by engineer. */
"Bridged Settings" = "桥接设置";

/* No comment provided by engineer. */
"By default, the best backend for the target will be used. If the selected backend is not available for any reason, an alternative will automatically be selected." = "默认情況下，将会使用目标虚拟机的最佳后端。若所选的后端由于任何原因而不可用，将自动选择替代方案。";

/* No comment provided by engineer. */
"By default, the best renderer for this device will be used. You can override this with to always use a specific renderer. This only applies to QEMU VMs with GPU accelerated graphics." = "默认情況下，将会使用最适合此设备的渲染器。你可以覆盖此选项，以始终使用特定的渲染器。此选项仅适用于具有 GPU 加速图形的 QEMU 虚拟机。";

/* No comment provided by engineer. */
"Calculating current size..." = "计算当前大小…";

/* No comment provided by engineer. */
"Cancel Download" = "取消下载";

/* No comment provided by engineer. */
"Change…" = "更改…";

/* No comment provided by engineer. */
"Clear…" = "清除…";

/* No comment provided by engineer. */
"Clipboard Sharing" = "剪贴板共享";

/* No comment provided by engineer. */
"Clone" = "复制";

/* No comment provided by engineer. */
"Clone selected VM" = "复制已选择的虚拟机";

/* No comment provided by engineer. */
"Clone…" = "复制…";

/* No comment provided by engineer. */
"Close" = "关闭";

/* No comment provided by engineer. */
"Closing a VM without properly shutting it down could result in data loss." = "在不正确关闭电源的情况下关闭虚拟机可能会导致数据丢失。";

/* No comment provided by engineer. */
"Compress" = "压缩";

/* No comment provided by engineer. */
"Compress by re-converting the disk image and compressing the data." = "通过重新转换磁盘映像和压缩数据映像来实现压缩。";

/* No comment provided by engineer. */
"Create a new VM" = "创建一个新虚拟机";

/* No comment provided by engineer. */
"Create a new VM with the same configuration as this one but without any data." = "创建一个与此配置相同，但不带有任何数据的新虚拟机。";

/* No comment provided by engineer. */
"Create an empty drive." = "创建一个空驱动器。";

/* No comment provided by engineer. */
"Debian Install Guide" = "Debian 安装指南";

/* No comment provided by engineer. */
"Default is 1/4 of the RAM size (above). The JIT cache size is additive to the RAM size in the total memory usage!" = "默认为内存大小的 1/4 (见上)。JIT 缓存与内存的大小均会包含在总内存使用量中！";

/* No comment provided by engineer. */
"Delete this drive." = "删除此驱动器。";

/* No comment provided by engineer. */
"Delete selected VM" = "刪除已选择的虚拟机";

/* No comment provided by engineer. */
"Delete this shortcut. The underlying data will not be deleted." = "删除此快捷方式。快捷方式背后指向的数据不会被删除。";

/* No comment provided by engineer. */
"Delete this VM and all its data." = "刪除此虚拟机及其所有数据。";

/* No comment provided by engineer. */
"Delete Drive" = "刪除驱动器";

/* No comment provided by engineer. */
"Description" = "注释";

/* No comment provided by engineer. */
"Devices" = "设备";

/* No comment provided by engineer. */
"Directory" = "目录";

/* No comment provided by engineer. */
"Directory Share Mode" = "目录共享模式";

/* No comment provided by engineer. */
"Disk" = "磁盘";

/* No comment provided by engineer. */
"DHCP Domain Name" = "DHCP 域名";

/* No comment provided by engineer. */
"DHCP End" = "DHCP 结束地址";

/* No comment provided by engineer. */
"DNS Search Domains" = "DNS 搜索域";

/* No comment provided by engineer. */
"DNS Server" = "DNS 服务器";

/* No comment provided by engineer. */
"DNS Server (IPv6)" = "DNS 服务器 (IPv6)";

/* No comment provided by engineer. */
"DHCP Start" = "DHCP 起始地址";

/* No comment provided by engineer. */
"Done" = "完成";

/* No comment provided by engineer. */
"Duplicate this VM along with all its data." = "复制此虚拟机及其所有数据。";

/* No comment provided by engineer. */
"Download and mount the guest support package for Windows. This is required for some features including dynamic resolution and clipboard sharing." = "下载并装载 Windows 客户机支持包。此支持包对于一些功能而言为必需，包括动态分辨率与剪贴板共享。";

/* No comment provided by engineer. */
"Download and mount the guest tools for Windows." = "下载并装载 Windows 客户机工具。";

/* No comment provided by engineer. */
"Download Windows 11 for ARM64 Preview VHDX" = "下载 Windows 11 ARM64 预览版 VHDX 映像";

/* No comment provided by engineer. */
"Downscaling" = "细化";

/* No comment provided by engineer. */
"Dynamic Resolution" = "动态";

/* No comment provided by engineer. */
"Edit" = "编辑";

/* No comment provided by engineer. */
"Edit selected VM" = "编辑已选择的虚拟机";

/* No comment provided by engineer. */
"Edit…" = "编辑…";

/* No comment provided by engineer. */
"Eject…" = "推出…";

/* No comment provided by engineer. */
"Emulated Audio Card" = "模拟声卡";

/* No comment provided by engineer. */
"Emulated Display Card" = "模拟显卡";

/* No comment provided by engineer. */
"Emulated Network Card" = "模拟网卡";

/* No comment provided by engineer. */
"Emulated Serial Device" = "模拟串行设备";

/* No comment provided by engineer. */
"Enable Balloon Device" = "启用 Balloon 设备";

/* No comment provided by engineer. */
"Enable Entropy Device" = "启用 Entropy 设备";

/* No comment provided by engineer. */
"Enable hardware OpenGL acceleration" = "启用 OpenGL 硬件加速";

/* No comment provided by engineer. */
"Enable Keyboard" = "启用键盘";

/* No comment provided by engineer. */
"Enable Pointer" = "启用光标";

/* No comment provided by engineer. */
"Enable Rosetta (x86_64 Emulation)" = "启用 Rosetta (x86_64 模拟)";

/* No comment provided by engineer. */
"Enable Rosetta on Linux (x86_64 Emulation)" = "在 Linux 中启用 Rosetta (x86_64 模拟)";

/* No comment provided by engineer. */
"Enable Sound" = "启用声音";

/* No comment provided by engineer. */
"Enable UTM Server" = "启用 UTM 服务器";

/* No comment provided by engineer. */
"Engine" = "引擎";

/* No comment provided by engineer. */
"Export all arguments as a text file. This is only for debugging purposes as UTM's built-in QEMU differs from upstream QEMU in supported arguments." = "将所有参数导出为文本文件。此功能仅用于调试目的，因为 UTM 的内置 QEMU 与上游 QEMU 在支持的参数上有所不同。";

/* No comment provided by engineer. */
"Export Debug Log" = "导出调试日志";

/* No comment provided by engineer. */
"External Drive" = "外部磁盘";

/* UTMData */
"Failed to parse download URL." = "无法解析下载 URL。";

/* No comment provided by engineer. */
"Fetch latest Windows installer…" = "获取最新的 Windows 安装程序…";

/* No comment provided by engineer. */
"Font" = "字体";

/* No comment provided by engineer. */
"Force Disable CPU Flags" = "强制停用 CPU 标志";

/* No comment provided by engineer. */
"Force Enable CPU Flags" = "强制启用 CPU 标志";

/* No comment provided by engineer. */
"Force multicore may improve speed of emulation but also might result in unstable and incorrect emulation." = "强制多核可以提高模拟速度，但也可能导致模拟不稳定和不正确。";

/* No comment provided by engineer. */
"Force PS/2 controller" = "强制使用 PS/2 控制器";

/* No comment provided by engineer. */
"FPS Limit" = "FPS 限制";

/* No comment provided by engineer. */
"Go Back" = "返回";

/* No comment provided by engineer. */
"GPU Acceleration Supported" = "支持 GPU 加速";

/* No comment provided by engineer. */
"Guest Address" = "客户机地址";

/* No comment provided by engineer. */
"Guest Network" = "客户机网络";

/* No comment provided by engineer. */
"Guest Network (IPv6)" = "客户机网络 (IPv6)";

/* No comment provided by engineer. */
"Guest Port" = "客户机端口";

/* No comment provided by engineer. */
"Hardware interface on the guest used to mount this image. Different operating systems support different interfaces. The default will be the most common interface." = "用于装载此映像的客户机硬件接口。不同的操作系统支持不同的接口。默认值为最常用的接口。";

/* No comment provided by engineer. */
"Hardware OpenGL Acceleration" = "硬件 OpenGL 加速";

/* No comment provided by engineer. */
"Height" = "高度";

/* No comment provided by engineer. */
"Hide" = "隐藏";

/* No comment provided by engineer. */
"Hide dock icon on next launch" = "下次启动时隐藏程序坞图标";

/* No comment provided by engineer. */
"Host Address" = "主机地址";

/* No comment provided by engineer. */
"Host Address (IPv6)" = "主机地址 (IPv6)";

/* No comment provided by engineer. */
"Host Port" = "主机端口";

/* No comment provided by engineer. */
"If checked, no drive image will be stored with the VM. Instead you can mount/unmount image while the VM is running." = "若选中，虚拟机将不会存储驱动器映像。相反，你可以在虚拟机运行时装载/卸载映像。";

/* No comment provided by engineer. */
"If checked, the CPU flag will be enabled. Otherwise, the default value will be used." = "若选中，将启用 CPU 标志。否则将使用默认值。";

/* No comment provided by engineer. */
"If checked, the CPU flag will be disabled. Otherwise, the default value will be used." = "若选中，将停用 CPU 标志。否则将使用默认值。";

/* No comment provided by engineer. */
"If checked, the drive image will be stored with the VM." = "若选中，驱动器映像将和虚拟机一起存储。";

/* No comment provided by engineer. */
"If checked, use local time for RTC which is required for Windows. Otherwise, use UTC clock." = "若选中，将使用 Windows 需要的 RTC 本地时间，否则使用 UTC 时钟。";

/* VMConfigAppleDriveDetailsView
 VMConfigAppleDriveCreateView*/
"If checked, use NVMe instead of virtio as the disk interface, available on macOS 14+ for Linux guests only. This interface is slower but less likely to encounter filesystem errors." = "若选中，将使用 NVMe 而不是 virtio 作为磁盘接口，仅适用于 macOS 14+ 上的 Linux 客户机。此接口速度较慢，但不太容易遇到文件系统错误。";

/* No comment provided by engineer. */
"If disabled, the default combination Control+Option (⌃+⌥) will be used." = "若停用，将使用默认组合键 Control + Option (⌃ + ⌥)。";

/* No comment provided by engineer. */
"If enabled, a virtiofs share tagged 'rosetta' will be available on the Linux guest for installing Rosetta for emulating x86_64 on ARM64." = "若启用，标为“rosetta”的 virtiofs 共享将在 Linux 客户机上可用，用于安装 Rosetta，并在 arm64 上模拟 x86_64。";

/* No comment provided by engineer. */
"If enabled, any existing screenshot will be deleted the next time the VM is started." = "若启用，下次启动虚拟机时，任何现有的快照都将被删除。";

/* No comment provided by engineer. */
"If enabled, caps lock will be handled like other keys. If disabled, it is treated as a toggle that is synchronized with the host." = "若启用，Caps Lock 将和其他按键一样处理。若停用，它将被视为与主机同步的切换键。";

/* No comment provided by engineer. */
"If enabled, input capture will toggle automatically when entering and exiting full screen mode." = "若启用，输入捕捉会在进入和退出全屏模式时自动切换。";

/* No comment provided by engineer. */
"If enabled, input capture will toggle automatically when the VM's window is focused." = "若启用，输入捕捉将在虚拟机窗口聚焦时自动切换。";

/* No comment provided by engineer. */
"If enabled, num lock will always be on to the guest. Note this may make your keyboard's num lock indicator out of sync." = "若启用，Num Lock 将始终对客户机开启。注意，这可能会使键盘上的 Num Lock 指示灯不同步。";

/* No comment provided by engineer. */
"If enabled, Option will be mapped to the Meta key which can be useful for emacs. Otherwise, option will work as the system intended (such as for entering international text)." = "若启用，Option 键将映射到 Meta 键，这对 Emacs 很有用。否则，Option 键将按照系统默认方式工作 (例如输入国际文本)。";

/* No comment provided by engineer. */
"If enabled, resizing of the VM window will not be allowed." = "若启用，将不允许调整虚拟机窗口的大小。";

/* No comment provided by engineer. */
"If enabled, scroll wheel input will be inverted." = "若启用，将反转滚轮输入。";

/* No comment provided by engineer. */
"If enabled, the default input devices will be emulated on the USB bus." = "若启用，默认输入设备将在 USB 总线上模拟。";

/* No comment provided by engineer. */
"If set, a frame limit can improve smoothness in rendering by preventing stutters when set to the lowest value your device can handle." = "若设置，则当设置为设备可以处理的最低值时，帧限制可以防止卡顿，从而提高渲染的流畅度。";

/* No comment provided by engineer. */
"If set, boot directly from a raw kernel image and initrd. Otherwise, boot from a supported ISO." = "若设置，将直接通过 raw 内核映像和 initrd 启动。否则通过受支持的 ISO 启动。";

/* No comment provided by engineer. */
"Image Type" = "映像类型";

/* No comment provided by engineer. */
"Import Drive…" = "导入驱动器…";

/* No comment provided by engineer. */
"Import VHDX Image" = "导入 VHDX 映像";

/* No comment provided by engineer. */
"Increase the size of the disk image." = "增加磁盘映像的大小。";

/* No comment provided by engineer. */
"Initial Ramdisk" = "初始 ramdisk";

/* No comment provided by engineer. */
"Input" = "输入";

/* No comment provided by engineer. */
"Install drivers and SPICE tools" = "安装驱动程序和 SPICE 工具";

/* No comment provided by engineer. */
"Install Windows 10 or higher" = "安装 Windows 10 或更高版本";

/* No comment provided by engineer. */
"Installation Instructions" = "安装指南";

/* No comment provided by engineer. */
"Instantiate PS/2 controller even when USB input is supported. Required for older Windows." = "即便支持 USB 输入，仍然实例化 PS/2 控制器。此功能对于旧版 Windows 为必需。";

/* No comment provided by engineer. */
"Interface" = "接口";

/* No comment provided by engineer. */
"IPSW Install Image" = "IPSW 安装映像";

/* No comment provided by engineer. */
"JIT Cache" = "JIT 缓存数据";

/* No comment provided by engineer. */
"Kernel" = "内核";

/* No comment provided by engineer. */
"Kernel Image" = "内核映像";

/* No comment provided by engineer. */
"Keyboard" = "键盘";

/* No comment provided by engineer. */
"Last Seen" = "上次记录于";

/* No comment provided by engineer. */
"Legacy Hardware" = "旧版硬件";

/* No comment provided by engineer. */
"MAC Address" = "MAC 地址";

/* No comment provided by engineer. */
"Machine" = "虚拟机";

/* No comment provided by engineer. */
"Maintenance" = "维护";

/* No comment provided by engineer. */
"Mode" = "模式";

/* No comment provided by engineer. */
"Modify settings for this VM." = "修改此虚拟机的设置。";

/* UTMAppleConfigurationDevices */
"Mouse" = "鼠标";

/* No comment provided by engineer. */
"Move" = "移动";

/* No comment provided by engineer. */
"Move…" = "移动…";

/* No comment provided by engineer. */
"Move selected VM" = "移动已选择的虚拟机";

/* No comment provided by engineer. */
"Move this VM from internal storage to elsewhere." = "将此虚拟机从内部存储空间移动到其他地方。";

/* No comment provided by engineer. */
"Network" = "网络";

/* No comment provided by engineer. */
"Network Mode" = "网络模式";

/* No comment provided by engineer. */
"New Drive…" = "新建驱动器…";

/* No comment provided by engineer. */
"New from template…" = "从此模板新建…";

/* No comment provided by engineer. */
"New Shared Directory…" = "新建共享目录…";

/* No comment provided by engineer. */
"New VM" = "新建虚拟机";

/* No comment provided by engineer. */
"Older versions of UTM added each IDE device to a separate bus. Check this to change the configuration to place two units on each bus." = "旧版本的 UTM 会将每个 IDE 设备添加到单独的总线上。选中此项可更改配置，以在每个总线上放置两个设备。";

/* No comment provided by engineer. */
"One Time Donation" = "一次性捐赠";

/* No comment provided by engineer. */
"Only available if host architecture matches the target. Otherwise, TCG emulation is used." = "仅当主机架构和目标匹配时才可用。否则，将使用 TCG 模拟。";

/* No comment provided by engineer. */
"Only available on macOS virtual machines." = "仅限 macOS 虚拟机可用。";

/* No comment provided by engineer. */
"Only available when Hypervisor is used on supported hardware. TSO speeds up Intel emulation in the guest at the cost of decreased performance in general." = "只有在支持的硬件上使用 Hypervisor 时才可用。TSO 加快了客户机中的 Intel 模拟速度，但总体性能却有所下降。";

/* No comment provided by engineer. */
"Open VM Settings" = "打开虚拟机设置";

/* No comment provided by engineer. */
"Optionally select a directory to make accessible inside the VM. Note that support for shared directories varies by the guest operating system and may require additional guest drivers to be installed. See UTM support pages for more details." = "(可选) 选择一个可在虚拟机内访问的目录。请注意，客户操作系统对共享目录的支持各不相同，可能需要安装额外的客户驱动程序。有关详细信息，请参阅 UTM 支持页面。";

/* No comment provided by engineer. */
"Options here only apply on next boot and are not saved." = "此处的选项只在下次启动时生效，不会存储。";

/* No comment provided by engineer. */
"Path" = "路径";

/* No comment provided by engineer. */
"Pointer" = "鼠标指针";

/* No comment provided by engineer. */
"Port" = "端口";

/* No comment provided by engineer. */
"Power Off" = "关闭电源";

/* No comment provided by engineer. */
"Prompt" = "提示";

/* No comment provided by engineer. */
"Protocol" = "协议";

/* No comment provided by engineer. */
"QEMU Machine Properties" = "QEMU 虚拟机属性";

/* No comment provided by engineer. */
"Quit" = "退出";

/* No comment provided by engineer. */
"RAM" = "内存";

/* No comment provided by engineer. */
"Ramdisk (optional)" = "Ramdisk (选填)";

/* No comment provided by engineer. */
"Random" = "随机";

/* No comment provided by engineer. */
"Read Only?" = "只读？";

/* No comment provided by engineer. */
"Reclaim disk space by re-converting the disk image." = "通过重新转换来回收磁盘空间。";

/* No comment provided by engineer. */
"Reclaim Space" = "释放空间";

/* No comment provided by engineer. */
"Reject unknown connections by default" = "默认情况下拒绝未知连接";

/* No comment provided by engineer. */
"Remove selected shortcut" = "移除选择的快捷方式";

/* No comment provided by engineer. */
"Renderer Backend" = "渲染器后端";

/* No comment provided by engineer. */
"Require Password" = "需要密码";

/* No comment provided by engineer. */
"Requires restarting UTM to take affect." = "需要重新打开 UTM 以生效。";

/* No comment provided by engineer. */
"Requires SPICE guest agent tools to be installed." = "需要安装 SPICE 客户机代理工具。";

/* No comment provided by engineer. */
"Reset UEFI Variables" = "重置 UEFI 变量";

/* No comment provided by engineer. */
"Resize Console Command" = "调整控制台大小指令";

/* No comment provided by engineer. */
"Resize…" = "调整大小…";

/* No comment provided by engineer. */
"Resizing is experimental and could result in data loss. You are strongly encouraged to back-up this VM before proceeding. Would you like to resize to %lld GiB?" = "调整大小是实验性功能，可能会导致数据丢失。强烈建议你在继续操作前备份此虚拟机。你要将大小调整为 %lld GiB 吗？";

/* No comment provided by engineer. */
"Resolution" = "分辨率";

/* No comment provided by engineer. */
"Restart" = "重新启动";

/* No comment provided by engineer. */
"Resume" = "继续";

/* No comment provided by engineer. */
"Resume running VM." = "继续正在运行的虚拟机。";

/* No comment provided by engineer. */
"Reveal where the VM is stored." = "显示虚拟机的存储位置。";

/* No comment provided by engineer. */
"RNG Device" = "RNG 设备";

/* No comment provided by engineer. */
"Root Image" = "Root 映像";

/* No comment provided by engineer. */
"Run" = "运行";

/* No comment provided by engineer. */
"Run Recovery" = "运行 Recovery 模式";

/* No comment provided by engineer. */
"Run selected VM" = "运行已选择的虚拟机";

/* No comment provided by engineer. */
"Run the VM in the foreground." = "在前台运行虚拟机。";

/* No comment provided by engineer. */
"Run the VM in the foreground, without saving data changes to disk." = "在前台运行虚拟机，但不会将数据存储到磁盘。";

/* No comment provided by engineer. */
"Run without saving changes" = "运行但不存储更改";

/* No comment provided by engineer. */
"Section" = "区域";

/* No comment provided by engineer. */
"Secure Boot with TPM 2.0" = "使用 TPM 2.0 的安全启动";

/* No comment provided by engineer. */
"Select an existing disk image." = "选择一个现有的磁盘映像。";

/* No comment provided by engineer. */
"Serial" = "串行";

/* No comment provided by engineer. */
"Server Address" = "服务器地址";

/* No comment provided by engineer. */
"Settings" = "设置";

/* No comment provided by engineer. */
"Share" = "共享";

/* No comment provided by engineer. */
"Share…" = "共享…";

/* No comment provided by engineer. */
"Share a copy of this VM and all its data." = "共享此虚拟机及其所有数据的副本。";

/* No comment provided by engineer. */
"Share Directory" = "共享目录";

/* No comment provided by engineer. */
"Shared Directory: %@" = "共享目录：%@";

/* No comment provided by engineer. */
"Share is read only" = "共享为只读";

/* No comment provided by engineer. */
"Share selected VM" = "共享已选择的虚拟机";

/* No comment provided by engineer. */
"Shared Directory Path" = "共享目录路径";

/* No comment provided by engineer. */
"Shared Path" = "共享路径";

/* No comment provided by engineer. */
"Should be off for older operating systems such as Windows 7 or lower." = "对于较旧的操作系统应关闭此选项，例如 Windows 7 或者更低版本。";

/* No comment provided by engineer. */
"Should be on always unless the guest cannot boot because of this." = "除非客户机因此无法启动，否则此项应始终打开。";

/* No comment provided by engineer. */
"Show all devices…" = "显示所有设备…";

/* No comment provided by engineer. */
"Show in Finder" = "在访达中显示";

/* No comment provided by engineer. */
"Show the main window." = "显示主窗口。";

/* No comment provided by engineer. */
"Show UTM" = "显示 UTM";

/* No comment provided by engineer. */
"Show UTM preferences" = "显示 UTM 偏好设置";

/* No comment provided by engineer. */
"Skip Boot Image" = "跳过启动映像";

/* No comment provided by engineer. */
"Skip ISO boot" = "跳过 ISO 启动";

/* No comment provided by engineer. */
"Some older systems do not support UEFI boot, such as Windows 7 and below." = "一些旧版系统不支持 UEFI 启动，例如 Windows 7 及更低版本。";

/* No comment provided by engineer. */
"Sound" = "声音";

/* No comment provided by engineer. */
"Sound Backend" = "声音后端";

/* No comment provided by engineer. */
"Start" = "开始";

/* No comment provided by engineer. */
"Status" = "状态";

/* No comment provided by engineer. */
"Stop selected VM" = "停止已选择的虚拟机";

/* No comment provided by engineer. */
"Stop the running VM." = "停止正在运行的虚拟机。";

/* No comment provided by engineer. */
"Storage" = "存储空间";

/* No comment provided by engineer. */
"stty cols $COLS rows $ROWS\n" = "stty 行 $ROWS 列 $COLS\n";

/* No comment provided by engineer. */
"Suspend" = "挂起";

/* No comment provided by engineer. */
"Target" = "目标";

/* No comment provided by engineer. */
"Terminate UTM and stop all running VMs." = "终止 UTM 并停止所有正在运行的虚拟机。";

/* No comment provided by engineer. */
"Text" = "文字";

/* No comment provided by engineer. */
"Text Color" = "文字颜色";

/* No comment provided by engineer. */
"The amount of storage to allocate for this image. Ignored if importing an image. If this is a raw image, then an empty file of this size will be stored with the VM. Otherwise, the disk image will dynamically expand up to this size." = "为此映像分配的存储空间。在导入映像时，此参数会被忽略。若导入的是 raw 映像，则虚拟机将存储一个与此相同大小的空文件。否则，磁盘映像将动态扩展至此大小。";

/* No comment provided by engineer. */
"Theme" = "主题";

/* No comment provided by engineer. */
"There are known issues in some newer Linux drivers including black screen, broken compositing, and apps failing to render." = "一些较新的 Linux 驱动程序存在已知问题，包括黑屏、合成失败和应用程序无法渲染。";

/* No comment provided by engineer. */
"These are advanced settings affecting QEMU which should be kept default unless you are running into issues." = "这些属于 QEMU 的高级设置，除非遇到问题，否则应当保持默认值。";

/* No comment provided by engineer. */
"This is appended to the -machine argument." = "这会添加到 -machine 参数的末端。";

/* No comment provided by engineer. */
"This virtual machine cannot be found at: %@" = "虚拟机无法通过此路径找到：%@";

/* No comment provided by engineer. */
"This virtual machine must be re-added to UTM by opening it with Finder. You can find it at the path: %@" = "必须使用访达打开此虚拟机，将其重新添加到 UTM 中。你可以通过此路径找到：%@";

/* No comment provided by engineer. */
"TPM 2.0 Device" = "TPM 2.0 设备";

/* No comment provided by engineer. */
"TPM can be used to protect secrets in the guest operating system. Note that the host will always be able to read these secrets and therefore no expectation of physical security is provided." = "TPM 可用于保护客户机操作系统中的机密数据。需要注意的是，主机始终可以读取这些机密数据，因此无法提供预期的物理安全性。";

/* UTMAppleConfigurationDevices */
"Trackpad" = "触控板";

/* No comment provided by engineer. */
"Tweaks" = "调整";

/* No comment provided by engineer. */
"Ubuntu Install Guide" = "Ubuntu 安装指南";

/* No comment provided by engineer. */
"UEFI Boot" = "UEFI 启动";

/* No comment provided by engineer. */
"Upscaling" = "粗化";

/* No comment provided by engineer. */
"USB Support" = "USB 支持";

/* No comment provided by engineer. */
"Use Apple Virtualization" = "使用 Apple 虚拟化";

/* No comment provided by engineer. */
"Use Hypervisor" = "使用 Hypervisor";

/* No comment provided by engineer. */
"Use local time for base clock" = "使用本地时间作为基本时钟";

/* VMConfigAppleDriveDetailsView
 VMConfigAppleDriveCreateView*/
"Use NVMe Interface" = "使用 NVMe 磁盘接口";

/* No comment provided by engineer. */
"Use Rosetta" = "使用 Rosetta";

/* No comment provided by engineer. */
"Use Trackpad" = "使用触控板";

/* No comment provided by engineer. */
"Use TSO" = "使用 TSO";

/* No comment provided by engineer. */
"Use Virtualization" = "使用虚拟化";

/* No comment provided by engineer. */
"UTM Server" = "UTM 服务器";

/* No comment provided by engineer. */
"VGA Device RAM (MB)" = "VGA 设备内存 (MB)";

/* No comment provided by engineer. */
"Virtualization" = "虚拟化";

/* No comment provided by engineer. */
"Virtualization Engine" = "虚拟化引擎";

/* No comment provided by engineer. */
"VM display size is fixed" = "固定虚拟机显示大小";

/* No comment provided by engineer. */
"Wait for Connection" = "等待连接";

/* No comment provided by engineer. */
"WebDAV requires installing SPICE daemon. VirtFS requires installing device drivers." = "WebDAV 需要安装 SPICE 守护程序。VirtFS 需要安装设备驱动程序。";

/* No comment provided by engineer. */
"Width" = "宽度";

/* No comment provided by engineer. */
"Windows Install Guide" = "Windows 安装指南";

/* No comment provided by engineer. */
"You can use this if your boot options are corrupted or if you wish to re-enroll in the default keys for secure boot." = "若你的启动选项已损坏，或者希望重新注册安全启动的默认密钥，可以使用此功能。";

/* No comment provided by engineer. */
"Zoom" = "缩放";
