/* A removable drive that has no image file inserted. */
"(empty)" = "(vuoto)";

/* VMConfigAppleDriveDetailsView */
"(New Drive)" = "(Nuovo Disco)";

/* No comment provided by engineer. */
"(new)" = "(nuovo)";

/* UTMWrappedVirtualMachine */
"(Unavailable)" = "(Non disponibile)";

/* QEMUConstant */
"%@ (%@)" = "%1$@ (%2$@)";

/* VMToolbarDriveMenuView */
"%@ (%@): %@" = "%1$@ (%2$@): %3$@";

/* VMDisplayMetalWindowController */
"%@ (Display %lld)" = "%1$@ (Monitor %2$lld)";

/* VMDisplayAppleTerminalWindowController
   VMDisplayQemuTerminalWindowController */
"%@ (Terminal %lld)" = "%1$@ (Terminale %2$lld)";

/* No comment provided by engineer. */
"%@ ➡️ %@" = "%1$@ ➡️ %2$@";

/* VMDrivesSettingsView */
"%@ Drive" = "Disco %@";

/* VMDrivesSettingsView */
"%@ Image" = "Immagine %@";

/* Format string for remaining time until a download finishes */
"%@ remaining" = "%@ rimanente";

/* Format string for the 'per second' part of a download speed. */
"%@/s" = "%@/s";

/* Format string for download progress and speed, e. g. 5 MB of 6 GB (200 kbit/s) */
"%1$@ of %2$@ (%3$@)" = "%1$@ di %2$@ (%3$@)";

/* UTMAppleConfiguration */
"A valid kernel image must be specified." = "È necessario specificare un'immagine del kernel valida";

/* VMConfigDriveCreateViewController */
"A file already exists for this name, if you proceed, it will be replaced." = "Un file con questo nome esiste già, se procedi, verrà sovrascritto";
"Cannot create directory for disk image." = "Non è stato possibile creare una cartella per l'immagine disco";

/* VMListViewController */
"A VM already exists with this name." = "È già presente una Macchina Virtuale con questo nome.";
"Cannot find VM." = "Macchina Virtuale non trovata.";

/* VMDisplayAppleController */
"Add…" = "Aggiungi…";

/* No comment provided by engineer. */
"Additional Options" = "Opzioni Aggiuntive";

/* No comment provided by engineer. */
"Additional Settings" = "Impostazioni Aggiuntive";

/* No comment provided by engineer. */
"Advanced: Bypass configuration and manually specify arguments" = "Avanzato: Ometti la configurazione e specifica gli argomenti";

/* No comment provided by engineer. */
"Advanced" = "Avanzate";

/* VMConfigSystemView */
"Allocating too much memory will crash the VM." = "Allocare troppa memoria causerà crash della Macchina Virtuale";
"Allocating too much memory will crash the VM. Your device has %llu MB of memory and the estimated usage is %llu MB." = "Allocare troppa memoria causerà crash della Macchina Virtuale. Il tuo dispositivo dispone di %llu MB di memoria e l'uso stimato è di %llu MB.";

/* VMConfigDirectoryPickerViewController */
"Are you sure you want to delete this directory? All files and subdirectories WILL be deleted." = "Sei sicuro di voler eliminare questa cartella? Tutti i file e le cartelle contenute saranno elimininati.";

/* Delete confirmation */
"Are you sure you want to delete this VM? Any drives associated will also be deleted." = "Sei sicuro di voler eliminare questa Macchina Virtuale? Ogni disco associato sarà eliminato.";

/* No comment provided by engineer. */
"Auto Resolution" = "Risoluzione Automatica";

/* UTMData */
"AltJIT error: %@" = "Errore AltJIT: %@";
"AltJIT error: (error.localizedDescription)" = "Errore AltJIT: (error.localizedDescription)";

/* No comment provided by engineer. */
"Always use native (HiDPI) resolution" = "Usa sempre la risoluzione nativa (HiDPI)";

/* UTMData */
"An existing virtual machine already exists with this name." = "È già presente una Macchina Virtuale con questo nome.";

/* CSConnection */
"An error occurred trying to connect to SPICE." = "Errore di connessione a SPICE.";

/* VMDrivesSettingsView */
"An image already exists with that name." = "È già presente un'immagine con questo nome.";

/* UTMConfiguration
   UTMVirtualMachine */
"An internal error has occurred." = "Si è verificato un errore interno";
"An internal error has occured. UTM will terminate." = "Si è verificato un errore interno. UTM verrà terminato.";
"Cannot start shared directory before SPICE starts." = "Impossibile condividere la directory prima dell'avvio di SPICE.";

/* No comment provided by engineer. */
"Argument" = "Argomento";

/* UTMConfiguration */
"An invalid value of '%@' is used in the configuration file." = "Un valore non corretto di “%@“ è presente nel file di configurazione.";

/* VMConfigSystemView */
"Any unsaved changes will be lost." = "Le modifiche non salvate andranno perse.";

/* No comment provided by engineer. */
"Application" = "Applicazione";

/* New VM window. */
"Apple Virtualization is experimental and only for advanced use cases. Leave unchecked to use QEMU, which is recommended." = "La Virtualizzazione Apple è sperimentale e consigliata per casi d'uso avanzati. È raccomandato lasciare questa opzione disabilitata per usare QEMU.";

/* No comment provided by engineer. */
"Architecture" = "Architettura";

/* No comment provided by engineer. */
"Are you sure you want to exit UTM?" = "Sei sicuro di voler uscire da UTM?";

/* No comment provided by engineer. */
"Are you sure you want to permanently delete this disk image?" = "Sei sicuro di voler eliminare questa immagine definitivamente?";

/* No comment provided by engineer. */
"Add a new drive." = "Aggiungi un nuovo disco.";

/* No comment provided by engineer. */
"Select an existing disk image." = "Selezione un'immagine disco esistente.";

/* No comment provided by engineer. */
"Create an empty drive." = "Crea un disco vuoto.";

/* No comment provided by engineer. */
"Add a new device." = "Aggiungi un nuovo dispositivo.";

/* No comment provided by engineer. */
"Are you sure you want to reset this VM? Any unsaved changes will be lost." = "Sei sicuro di voler riavviare questa Macchina Virtuale? Le modifiche non salvate andranno perse.";

/* No comment provided by engineer. */
"Are you sure you want to stop this VM and exit? Any unsaved changes will be lost." = "Sei sicuro di voler arrestare questa Macchina Virtuale e uscire? Le modifiche non salvate andranno perse.";

/* UTMQemuConstants */
"Automatic Serial Device (max 4)" = "Dispositivo seriale automatico (massimo 4)";

/* No comment provided by engineer. */
"Background Color" = "Colore di Sfondo";

/* No comment provided by engineer. */
"Balloon Device" = "Dispositivo Balloon";

/* No comment provided by engineer. */
"TPM can be used to protect secrets in the guest operating system. Note that the host will always be able to read these secrets and therefore no expectation of physical security is provided." = "TPM può essere utilizzato per conservare informazioni riservate del sistema. Il dispositivo host sarà sempre in grado di accedere a questi dati.";

/* UTMLegacyQemuConfiguration
   UTMQemuConstants */
"BIOS" = "BIOS";

/* No comment provided by engineer. */
"Blinking Cursor" = "Cursore Lampeggiante";

/* UTMQemuConstants */
"Bold" = "Grassetto";

/* No comment provided by engineer. */
"Boot" = "Avvio";

/* No comment provided by engineer. */
"Boot from kernel image" = "Avvia da immagine del kernel";

/* No comment provided by engineer. */
"Ubuntu Install Guide" = "Guida all'installazione di Ubuntu";

/* No comment provided by engineer. */
"Boot Arguments" = "Argomenti di Avvio";

/* No comment provided by engineer. */
"Boot Image" = "Immagine di Avvio";

/* No comment provided by engineer. */
"Boot Image Type" = "Tipo di Immagine di Avvio";

/* No comment provided by engineer. */
"Boot ISO Image" = "Immagine ISO di Avvio";

/* No comment provided by engineer. */
"Boot ISO Image (optional)" = "Immagine ISO di Avvio (opzionale)";

/* No comment provided by engineer. */
"Boot VHDX Image" = "Immagine VHDX di Avvio";

/* No comment provided by engineer. */
"Boot into recovery mode." = "Avvia con modalità di ripristino";

/* UTMQemuConstants */
"Bridged (Advanced)" = "Bridged (Avanzata)";

/* No comment provided by engineer. */
"Bridged Settings" = "Impostazioni Bridged";

/* No comment provided by engineer. */
"Bridged Interface" = "Interfaccia Bridged";

/* Welcome view */
"Browse UTM Gallery" = "Scopri la UTM Gallery";

/* No comment provided by engineer. */
"Browse" = "Scegli";

/* No comment provided by engineer. */
"Browse…" = "Scegli...";

/* UTMQemuConstants */
"Built-in Terminal" = "Terminale Integrato";

/* VMDisplayWindowController
   VMQemuDisplayMetalWindowController */
"Cancel" = "Annulla";

/* No comment provided by engineer. */
"Cancel download" = "Annulla Download";

/* UTMAppleVirtualMachine */
"Cannot access resource: %@" = "Impossibile accedere alla risorsa: %@";

/* UTMAppleVirtualMachine */
"Cannot create virtual terminal." = "Impossibile creare il terminale virtuale.";

/* UTMData */
"Cannot find AltServer for JIT enable. You cannot run VMs until JIT is enabled." = "Impossibile trovare AltServer per l'abilitazione di JIT. Non è possibile eseguire Macchine Virtuali senza che JIT sia abilitato.";

/* UTMData */
"Cannot import this VM. Either the configuration is invalid, created in a newer version of UTM, or on a platform that is incompatible with this version of UTM." = "Impossibile importare questa Macchina Virtuale. Potrebbe contenere una configurazione invalida, essere stata creata con una nuova versione di UTM, oppure su una piattaforma non supportata da questa versione di UTM.";

/* No comment provided by engineer. */
"Caps Lock (⇪) is treated as a key" = "Blocco Maiusc. (⇪) come tasto";

/* VMMetalView */
"Capture Input" = "Cattura Input";

/* VMDisplayQemuMetalWindowController */
"Captured mouse" = "Mouse catturato";

/* Configuration boot device */
"CD/DVD" = "CD/DVD";

/* UTMLegacyQemuConfiguration
   UTMQemuConstants */
"CD/DVD (ISO) Image" = "Immagine (ISO) di CD/DVD";

/* VMDisplayWindowController */
"Change" = "Cambia";

/* VMDisplayAppleController */
"Change…" = "Cambia…";

/* No comment provided by engineer. */
"Clear" = "Pulisci";

/* No comment provided by engineer. */
"Clipboard Sharing" = "Condivisione Appunti";

/* VMQemuDisplayMetalWindowController */
"Closing this window will kill the VM." = "Chiudere questa finestra terminerà la Macchina Virtuale";

/* Clone context menu */
"Clone" = "Clona";

/* No comment provided by engineer. */
"Clone selected VM" = "Clona la VM selezionata";

/* No comment provided by engineer. */
"Clone…" = "Clona...";

/* No comment provided by engineer. */
"Close" = "Chiudi";

/* No comment provided by engineer. */
"Command to send when resizing the console. Placeholder $COLS is the number of columns and $ROWS is the number of rows." = "Comando da inviare quando la console viene ridimensionata. La variabile $COLS è il numero di colonne, $ROWS è il numero di righe.";

/* UTMVirtualMachine */
"Config format incorrect." = "Formato di configurazione incorretto.";

/* VMQemuDisplayMetalWindowController */
"Confirm" = "Conferma";

/* No comment provided by engineer. */
"Confirm Delete" = "Conferma Eliminazione";

/* VMDisplayWindowController */
"Confirmation" = "Conferma";

/* No comment provided by engineer. */
"Connection" = "Connessione";

/* No comment provided by engineer. */
"Console Only" = "Solo Console";

/* VMWizardSummaryView */
"Core" = "Core";

/* No comment provided by engineer. */
"Cores" = "Core";

/* No comment provided by engineer. */
"Continue" = "Continua";

/* No comment provided by engineer. */
"CPU" = "CPU";

/* No comment provided by engineer. */
"CPU Cores" = "Core della CPU";

/* No comment provided by engineer. */
"CPU Flags" = "Flag della CPU";

/* No comment provided by engineer. */
"Create" = "Crea";

/* Welcome view */
"Create a New Virtual Machine" = "Crea una Nuova Macchina Virtuale";

/* No comment provided by engineer. */
"Create a new VM with the same configuration as this one but without any data." = "Crea una nuova Macchina Virtuale con la stessa configurazione, ma senza alcun dato.";

/* No comment provided by engineer. */
"Custom" = "Personalizzata";

/* VMConfigDirectoryPickerViewController */
"Create Directory" = "Crea Cartella";

/* VMConfigDriveCreateViewController */
"Creating disk…" = "Creazione disco...";

/* No comment provided by engineer. */
"Debug Logging" = "Registro dei Log";

/* QEMUConstantGenerated
   UTMQemuConstants */
"Default" = "Predefinito";

/* VMWizardSummaryView */
"Default Cores" = "Core Predefiniti";

/* No comment provided by engineer. */
"Default is 1/4 of the RAM size (above). The JIT cache size is additive to the RAM size in the total memory usage!" = "1/4 della RAM (sopra) come impostazione predefinita. La dimensione della cache JIT si aggiunge alla quantità di memoria utilizzata!";

/* No comment provided by engineer. */
"Delete" = "Elimina";

/* VMConfigDrivesViewController */
"Delete Data" = "Elimina Dati";

/* No comment provided by engineer. */
"Delete Drive" = "Elimina Disco";

/* No comment provided by engineer. */
"Delete this drive." = "Elimina questo disco";

/* No comment provided by engineer. */
"Delete selected VM" = "Elimina la VM selezionata";

/* No comment provided by engineer. */
"Delete…" = "Elimina...";

/* No comment provided by engineer. */
"Delete this shortcut. The underlying data will not be deleted." = "Elimina questa scorciatoia. I dati originali non saranno eliminati.";

/* No comment provided by engineer. */
"Delete this VM and all its data." = "Elimina questa Macchina Virtuale e tutti i suoi dati.";

/* Delete VM overlay */
"Deleting %@…" = "Eliminazione di %@...";

/* No comment provided by engineer. */
"DHCP Domain Name" = "Nome di Dominio DHCP";

/* No comment provided by engineer. */
"DHCP Host" = "Host DHCP";

/* No comment provided by engineer. */
"DHCP Start" = "Inzio DHCP";

/* No comment provided by engineer. */
"DHCP End" = "Fine DHCP";

/* No comment provided by engineer. */
"Directory" = "Cartella";

/* VMConfigDirectoryPickerViewController */
"Directory Name" = "Nome della Cartella";

/* No comment provided by engineer. */
"Devices" = "Dispositivi";

/* VMDisplayAppleWindowController */
"Directory sharing" = "Condivisione Cartella";

/* No comment provided by engineer. */
"Directory Share Mode" = "Modalità di Condivisione Cartelle";

/* UTMQemuConstants */
"Disabled" = "Disattivato";

/* VMDisplayTerminalViewController */
"Disable this bar in Settings -> General -> Keyboards -> Shortcuts" = "Disattiva questa barra in Impostazioni -> Generali -> Tastiere -> Scorciatoie";

/* No comment provided by engineer. */
"Disk" = "Disco";

/* UTMData
   VMConfigDriveCreateViewController
   VMWizardState */
"Disk creation failed." = "Creazione del disco fallita.";

/* UTMLegacyQemuConfiguration
   UTMQemuConstants */
"Disk Image" = "Immagine del Disco";

/* VMDisplayAppleWindowController */
"Display" = "Schermo";

/* VMDisplayQemuDisplayController */
"Display %lld: %@" = "Schermo %1$lld: %2$@";

/* VMDisplayQemuDisplayController */
"Disposable Mode" = "Modalità usa e getta";

/* No comment provided by engineer. */
"DNS Search Domains" = "Dominio di Ricerca DNS";

/* No comment provided by engineer. */
"DNS Server" = "Server DNS";

/* No comment provided by engineer. */
"DNS Server (IPv6)" = "Server DNS (IPv6)";

/* No comment provided by engineer. */
"Do not save VM screenshot to disk" = "Non salvare gli screenshot delle Macchine Virtuali su disco";

/* VMDisplayMetalWindowController */
"Do Not Show Again" = "Non Mostrare di Nuovo";

/* No comment provided by engineer. */
"Do not show prompt when USB device is plugged in" = "Non mostrare una richiesta quando un dispositivo USB viene collegato";

/* VMConfigDrivesViewController */
"Do you want to also delete the disk image data? If yes, the data will be lost. Otherwise, you can create a new drive with the existing data." = "Vuoi anche eliminare i dati dell'immagine disco? Se sì, i dati saranno persi. In caso contrario, puoi creare un nuovo disco con i dati esistenti.";

/* No comment provided by engineer. */
"Do you want to delete this VM and all its data?" = "Vuoi eliminare questa Macchina Virtuale e tutti i suoi dati?";

/* No comment provided by engineer. */
"Do you want to duplicate this VM and all its data?" = "Vuoi clonare questa Macchina Virtuale e tutti i suoi dati?";

/* No comment provided by engineer. */
"Do you want to force stop this VM and lose all unsaved data?" = "Vuoi forzare l'arresto di questa Macchina Virtuale e perdere tutti i dati non salvati?";

/* No comment provided by engineer. */
"Do you want to move this VM to another location? This will copy the data to the new location, delete the data from the original location, and then create a shortcut." = "Vuoi spostare questa Macchina Virtuale in una nuova posizione? I dati saranno copiati nella nuova posizione ed eliminati dalla posizione originale. Verrà creata una nuova scorciatoia.";

/* No comment provided by engineer. */
"Do you want to remove this shortcut? The data will not be deleted." = "Vuoi eliminare questa scorciatoia? I dati originali non saranno eliminati.";

/* VMConfigDirectoryPickerViewController
   VMConfigPortForwardingViewController */
"Done" = "Completato";

/* No comment provided by engineer. */
"Download and mount the guest support package for Windows. This is required for some features including dynamic resolution and clipboard sharing." = "Scarica e monta l'immagine del paccheto di strumenti di supporto per Windows. Questi sono necessari per alcune funzionalità come la risoluzione dinamica e la condivisione degli appunti.";

/* No comment provided by engineer. */
"Download and mount the guest tools for Windows." = "Scarica e monta gli strumenti guest per Windows.";

/* No comment provided by engineer. */
"Download prebuilt from UTM Gallery…" = "Scarica una VM pronta da usare da UTM Gallery...";

/* No comment provided by engineer. */
"Download Ubuntu Server for ARM" = "Scarica Ubuntu Server per ARM";

/* No comment provided by engineer. */
"Download Windows 11 for ARM64 Preview VHDX" = "Scarica l'immagine della Windows 11 ARM64 Preview VHDX";

/* No comment provided by engineer. */
"Downscaling" = "Downscaling";

/* No comment provided by engineer. */
"Drag and drop IPSW file here" = "Trascina il file IPSW qui";

/* VMRemovableDrivesViewController */
"Drive Options" = "Opzioni Disco";

/* No comment provided by engineer. */
"Drives" = "Dischi";

/* No comment provided by engineer. */
"Duplicate this VM along with all its data." = "Duplica questa Macchina Virtuale e tutti i suoi dati";

/* No comment provided by engineer. */
"Edit" = "Modifica";

/* No comment provided by engineer. */
"Edit…" = "Modifica...";

/* No comment provided by engineer. */
"Edit selected VM" = "Modifica la VM selezionata";

/* VMDrivesSettingsView */
"EFI Variables" = "Variabli EFI";

/* VMDisplayWindowController */
"Eject" = "Espelli";

/* New VM window. */
"Empty" = "Vuoto";

/* No comment provided by engineer. */
"Emulate" = "Emula";

/* No comment provided by engineer. */
"Emulated Audio Card" = "Scheda Audio Emulata";

/* No comment provided by engineer. */
"Emulated Display Card" = "Scheda Video Emulata";

/* No comment provided by engineer. */
"Emulated Network Card" = "Scheda di Rete Emulata";

/* UTMQemuConfiguration */
"Emulated VLAN" = "VLAN Emulata";

/* No comment provided by engineer. */
"Emulated Serial Device" = "Dispositivo Seriale Emulato";

/* No comment provided by engineer. */
"Enable Balloon Device" = "Abilita Dispositivo Balloon";

/* No comment provided by engineer. */
"Enable Entropy Device" = "Abilita Dispositivo Entropy";

/* No comment provided by engineer. */
"Enable Clipboard Sharing" = "Abilita Condivisione Appunti";

/* No comment provided by engineer. */
"Requires SPICE guest agent tools to be installed." = "Richiede l'installazione degli strumenti SPICE sul sistema guest.";

/* No comment provided by engineer. */
"Enable Directory Sharing" = "Abilita Condivisione Cartella";

/* No comment provided by engineer. */
"Enable Keyboard" = "Abilita Tastiera";

/* No comment provided by engineer. */
"Enable Sound" = "Abilita Suono";

/* No comment provided by engineer. */
"Enable Pointer" = "Abilita Puntatore";

"Enable Rosetta on Linux (x86_64 Emulation)" = "Abilita Rosetta su Linux (Emulazione x86_64)";

"If enabled, a virtiofs share tagged 'rosetta' will be available on the Linux guest for installing Rosetta for emulating x86_64 on ARM64." = "Se abilitato, verrà resa disponibile la condivisione virtiofs 'rosetta' al guest Linux, per poter installare Rosetta ed emulare x86_64 su ARM64.";

/* No comment provided by engineer. */
"Enable hardware OpenGL acceleration" = "Abilita l'accelerazione hardware OpenGL";

/* No comment provided by engineer. */
"Enabled" = "Abilitato";

/* No comment provided by engineer. */
"Engine" = "Engine";

/* VMDisplayWindowController */
"Error" = "Errore";

/* UTMJSONStream */
"Error parsing JSON." = "Errore di conversione JSON";

/* VMConfigDriveCreateViewController */
"Error renaming file" = "Errore di ridenominazione file";

/* UTMVirtualMachine */
"Error trying to restore external drives and shares: %@" = "Errore durante il tentativo di ripristino dei dischi esterni e delle condivisioni: %@";

/* UTMVirtualMachine */
"Error trying to start shared directory: %@" = "Errore durante l'avvio della cartella condivisa: %@";

/* No comment provided by engineer. */
"Existing" = "Esistente";

/* No comment provided by engineer. */
"Export Debug Log" = "Esporta Log di Debug";

/* No comment provided by engineer. */
"Export QEMU Command…" = "Esporta comando QEMU…";

/* No comment provided by engineer. */
"Export all arguments as a text file. This is only for debugging purposes as UTM's built-in QEMU differs from upstream QEMU in supported arguments." = "Esporta tutti gli argomenti in un file di testo. Consigliato per il debugging: gli argomenti QEMU utilizzati da UTM potrebbero differire da quelli di QEMU upstream";

/* Word for decompressing a compressed folder */
"Extracting…" = "Estrazione…";

/* UTMVirtualMachine+Drives */
"Failed create bookmark." = "Impossibile creare preferito.";

/* UTMQemuVirtualMachine */
"Failed to access data from shortcut." = "Impossibile accedere ai dati dalla scorciatoia.";

/* UTMQemuVirtualMachine */
"Failed to access drive image path." = "Impossibile accedere alla posizione dell'immagine disco.";

/* UTMQemuVirtualMachine */
"Failed to access shared directory." = "Impossibile accedere alla cartella condivisa.";

/* ContentView */
"Failed to attach to JitStreamer:\n%@" = "Errore di collegamento a JitStreamer: %@";

/* ContentView */
"Failed to attach to JitStreamer." = "Errore di collegamento a JitStreamer.";

/* VMConfigInfoView */
"Failed to check name." = "Impossibile verificare il nome.";

/* UTMData */
"Failed to clone VM." = "Impossibile clonare la Macchina Virtuale";

/* UTMSpiceIO */
"Failed to connect to SPICE server." = "Collegamento al server SPICE fallito";

/* ContentView */
"Failed to decode JitStreamer response." = "Impossibile interpretare la risposta di JitStreamer.";

/* UTMDataExtension */
"Failed to delete saved state." = "Salvataggio dello stato fallito.";

/* VMWizardState */
"Failed to get latest macOS version from Apple." = "Non è stato possibile ottenere l'ultima versione di macOS da Apple.";

/* VMRemovableDrivesViewController */
"Failed to get VM object." = "Non è stato possibile ottenere l'oggetto della VM";

/* UTMVirtualMachine */
"Failed to load plist" = "Caricamento del plist fallito";

/* UTMQemuConfigurationError */
"Failed to migrate configuration from a previous UTM version." = "Migrazione da una precedente versione di UTM fallita.";

/* UTMData */
"Failed to parse download URL." = "Errore nella lettura dell'URL di download.";

/* UTMData */
"Failed to parse imported VM." = "Errore di lettura della la Macchina Virtuale importata";

/* UTMDownloadVMTask */
"Failed to parse the downloaded VM." = "Errore di lettura della la Macchina Virtuale scaricata.";

/* UTMQemuVirtualMachine */
"Failed to save VM snapshot. Usually this means at least one device does not support snapshots. %@" = "Non è stato possibile salvare l'istantanea della VM. Generalmente, questo significa che almeno uno dei dispositivi non supporta le istantanee: %@";
"Failed to save VM snapshot. Usually this means at least one device does not support snapshots." = "Non è stato possibile salvare l'istantanea della VM. Generalmente, questo significa che almeno uno dei dispositivi non supporta le istantanee.";

/* UTMSpiceIO */
"Failed to start SPICE client." = "Errore di avvio del Client SPICE";

/* No comment provided by engineer. */
"Faster, but can only run the native CPU architecture." = "Più veloce, ma può solo eseguire VM con l'architettura nativa della CPU.";

/* No comment provided by engineer. */
"Fit To Screen" = "Adatta allo Schermo";

/* No comment provided by engineer. */
"Font" = "Font";

/* No comment provided by engineer. */
"Font Size" = "Dimensione Font";

/* VMDisplayQemuDisplayController */
"Force kill" = "Arresto Forzato";

/* No comment provided by engineer. */
"Force Multicore" = "Forza Multicore";

/* No comment provided by engineer. */
"Force multicore may improve speed of emulation but also might result in unstable and incorrect emulation." = "Forzare l'uso del Multicore potrebbe incrementare la velocità emulazione, ma potrebbe risultare in emulazione scorretta o instabile.";

/* No comment provided by engineer. */
"Force PS/2 controller" = "Forza Controller PS/2";

/* No comment provided by engineer. */
"Force Enable CPU Flags" = "Forza Abilitazione Flag CPU";

/* No comment provided by engineer. */
"Force Disable CPU Flags" = "Forza Disattivazioen Flag CPU";

/* VMDisplayQemuDisplayController */
"Force shut down" = "Forza Spegnimento";

"Force kill the VM process with high risk of data corruption." = "Forza l'interruzione del processo della VM, con un elevato rischio di compromettere dei dati.";

/* No comment provided by engineer. */
"Full Graphics" = "Grafica Completa";

/* No comment provided by engineer. */
"GiB" = "GiB";

/* UTMQemuConstants */
"GDB Debug Stub" = "GDB Debug Stub";

/* No comment provided by engineer. */
"Generate Windows Installer ISO" = "Genera l'immagine ISO dell'Installer di Windows";

/* No comment provided by engineer. */
"Generic" = "Generico";

/* No comment provided by engineer. */
"Gesture and Cursor Settings" = "Impostazioni Gesti e Cursore";

/* VMWizardView */
"Go Back" = "Indietro";

/* No comment provided by engineer. */
"Guest Address" = "Indirizzo Guest";

/* VMConfigPortForwardingViewController */
"Guest address (optional)" = "Indirizzo Guest (opcional)";

/* No comment provided by engineer. */
"Guest Network" = "Rete Guest";

/* No comment provided by engineer. */
"Guest Network (IPv6)" = "Rete Guest (IPv6)";

/* UTMQemuManager */
"Guest panic" = "Guest panic";

/* No comment provided by engineer. */
"Guest Port" = "Porta Guest";

/* VMConfigPortForwardingViewController */
"Guest port (required)" = "Porta Guest (richiesto)";

/* Configuration boot device */
"Hard Disk" = "Disco Rigido";

/* No comment provided by engineer. */
"Hardware" = "Hardware";

/* No comment provided by engineer. */
"Hardware OpenGL Acceleration" = "Accelerazione Hardware OpenGL";

/* No comment provided by engineer. */
"Hello" = "Ciao";

/* No comment provided by engineer. */
"Hide" = "Nascondi";

/* No comment provided by engineer. */
"Hide Unused…" = "Nascondi inutilizzati...";

/* VMDisplayViewController */
"Hint: To show the toolbar again, use a three-finger swipe down on the screen." = "Suggerimento: per mostrare di nuovo la barra degli strumenti, scorri con tre dita sullos chermo.";

/* No comment provided by engineer. */
"Hold Control (⌃) for right click" = "Tieni premuto Control (⌃) per il click destro";

/* No comment provided by engineer. */
"Host Address" = "Indirizzo Host";

/* No comment provided by engineer. */
"Host Address (IPv6)" = "Indirizzo Host (IPv6)";

/* VMConfigPortForwardingViewController */
"Host address (optional)" = "Indirizzo Host (opcional)";

/* UTMQemuConstants */
"Host Only" = "Solo Host";

/* No comment provided by engineer. */
"Host Port" = "Porta Host";

/* VMConfigPortForwardingViewController */
"Host port (required)" = "Porta Host (requerido)";

/* No comment provided by engineer. */
"Hypervisor" = "Hypervisor";

/* No comment provided by engineer. */
"I want to…" = "Voglio...";

/* No comment provided by engineer. */
"If enabled, the default input devices will be emulated on the USB bus." = "Se abilitato, i dispositivi di input predefiniti saranno emulati sul bus USB.";

"If enabled, a virtiofs share tagged 'rosetta' will be available on the Linux guest for installing Rosetta for emulating x86_64 on ARM64." = "Se abilitato, una cartella condvisa virtiofs con nome 'rosetta' sarà disponibile sul guest Linux, permettendoti di installare Rosetta per emulare x86_64 su ARM64.";

/* No comment provided by engineer. */
"If checked, use local time for RTC which is required for Windows. Otherwise, use UTC clock." = "Se abilitato, usa l'ora locale per il RTC, richiesto da Windows. Altrimenti, usa UTC.";

/* No comment provided by engineer. */
"If checked, the CPU flag will be enabled. Otherwise, the default value will be used." = "Se abilitato, la flag CPU verrà abilitata. In alternativa, sarà usato il valore predefinito.";

/* No comment provided by engineer. */
"If checked, the CPU flag will be disabled. Otherwise, the default value will be used." = "Se abiltato, la flag CPU verrà disabilitata. In alternativa, sarà usato il valore predefinito.";

/* No comment provided by engineer. */
"Icon" = "Icona";

/* UTMQemuConstants */
"IDE" = "IDE";

/* No comment provided by engineer. */
"If set, boot directly from a raw kernel image and initrd. Otherwise, boot from a supported ISO." = "Se impostato, avvia direttamente da un'immagine del kernel e initrd. In alternativa, avvia da una ISO supportata.";

/* No comment provided by engineer. */
"Image File Type" = "Tipo di File di Immagine";

/* No comment provided by engineer. */
"Image Type" = "Tipo di Immagine";

/* No comment provided by engineer. */
"Import IPSW" = "Importa un IPSW";

/* No comment provided by engineer. */
"Import…" = "Importa...";

/* No comment provided by engineer. */
"Import Drive…" = "Importa Disco...";

/* No comment provided by engineer. */
"Import VHDX Image" = "Importa un'Immagine VHDX";

/* No comment provided by engineer. */
"Fetch latest Windows installer…" = "Ottieni l'installer di Windows più recente";

/* No comment provided by engineer. */
"Windows Install Guide" = "Guida all'installazione di Windows";

/* No comment provided by engineer. */
"Import Virtual Machine…" = "Importa una Macchina Virtuale...";

/* Save VM overlay */
"Importing %@…" = "Importazione di %@...";

/* VMDetailsView */
"Inactive" = "Inattivo";

/* No comment provided by engineer. */
"Information" = "Informazioni";

/* No comment provided by engineer. */
"Initial Ramdisk" = "RAMDisk Iniziale";

/* No comment provided by engineer. */
"Input" = "Input";

/* No comment provided by engineer. */
"Interface" = "Interfaccia";

"Hardware interface on the guest used to mount this image. Different operating systems support different interfaces. The default will be the most common interface." = "L'interfaccia hardware utilizzata dal sistema guest per montare questa imnmagine. Ciascun sistema operativo può supportare diversi tipi di interfacce. L'interfaccia predefinita sarà quella utilizzata comunemente.";

/* VMDisplayWindowController */
"Install Windows Guest Tools…" = "Installa gli Strumenti Guest per Windows…";

/* No comment provided by engineer. */
"Install Windows 10 or higher" = "Installa Windows 10 o successivo";

/* No comment provided by engineer. */
"Install drivers and SPICE tools" = "Installa i Driver e gli Strumenti SPICE";

/* VMDisplayAppleWindowController */
"Installation: %@" = "Installazione: %@";

/* No comment provided by engineer. */
"Instantiate PS/2 controller even when USB input is supported. Required for older Windows." = "Inizializza il controller PS/2 anche quando l'input USB è supportato. Richiesto per versioni più vecchie di Windows.";

/* UTMQemu */
"Internal error has occurred." = "Si è verificato un errore interno.";

/* UTMSpiceIO */
"Internal error trying to connect to SPICE server." = "Si è verificato un errore interno durante la connessione al server SPICE.";

/* UTMVirtualMachine */
"Internal error starting main loop." = "Si è verificato un errore interno all'avvio del loop principale.";

/* UTMVirtualMachine */
"Internal error starting VM." = "Si è verificato un errore interno all'avvio della VM.";

/* VMDisplayMetalWindowController */
"Internal error." = "Errore interno.";

/* ContentView */
"Invalid JitStreamer attach URL:\n%@" = "Attach URL di JitStramer non valido: %@";

/* VMConfigAppleNetworkingView */
"Invalid MAC address." = "MAC address non corretto.";

/* VMConfigSystemViewController */
"Invalid core count." = "Numero di core non valido.";

/* UTMData */
"Invalid drive size." = "Dimensione del disco non valida.";

/* VMRemovableDrivesViewController */
"Invalid file selected." = "File selezionato non valido.";

/* VMConfigSystemViewController */
"Invalid memory size." = "Dimensione della memoria non valida.";

/* VMConfigDriveCreateViewController */
"Invalid name" = "Nome non valido";

/* VMConfigDriveCreateViewController */
"Invalid size" = "Dimensione non valida";

/* VMListViewController */
"Invalid UTM not imported." = "UTM non valido non importato.";

/* No comment provided by engineer. */
"Invert Mouse Scroll" = "Inverti lo scorrimento del mouse";

/* No comment provided by engineer. */
"Invert scrolling" = "Inverti lo scorrimento";

/* No comment provided by engineer. */
"If enabled, scroll wheel input will be inverted." = "Se abilitato, lo scorrimento della rotella del mouse verrà invertito";

/* No comment provided by engineer. */
"IP Configuration" = "Impostazioni IP";

/* No comment provided by engineer. */
"Isolate Guest from Host" = "Isola Guest";

/* UTMQemuConstants */
"Italic" = "Cursivo";

/* UTMQemuConstants */
"Italic, Bold" = "Cursivo, Grassetto";

/* No comment provided by engineer. */
"JIT Cache" = "Cache JIT";

/* VMConfigSystemViewController */
"JIT cache size cannot be larger than 2GB." = "La dimensione della cache JIT non può essere superiore a 2GB.";

/* VMConfigSystemViewController */
"JIT cache size too small." = "Dimensione della cache JIT insufficiente.";

/* No comment provided by engineer. */
"Kernel" = "Kernel";

/* No comment provided by engineer. */
"Keyboard" = "Tastiera";

/* No comment provided by engineer. */
"Keep UTM running after last window is closed and all VMs are shut down" = "Esegui UTM anche quando tutte le fineste sono chiuse e nessuna VM è in esecuzione";

/* No comment provided by engineer. */
"Show dock icon" = "Mostra icona nel dock";

/* No comment provided by engineer. */
"Show menu bar icon" = "Mostra icona nella barra dei menu";

/* No comment provided by engineer. */
"Prevent system from sleeping when any VM is running" = "Impedisci lo stop del sistema quando una VM è in esecuzione";

/* No comment provided by engineer. */
"Do not show confirmation when closing a running VM" = "Non mostrare un avviso alla chiusura di una VM in esecuzione";

/* No comment provided by engineer. */
"Closing a VM without properly shutting it down could result in data loss." = "La chiusura di una VM senza il corretto spegnimento potrebbe comportare perdita di dati.";

/* No comment provided by engineer. */
"QEMU Graphics Acceleration" = "Accelerazione Grafica QEMU";

/* No comment provided by engineer. */
"By default, the best renderer for this device will be used. You can override this with to always use a specific renderer. This only applies to QEMU VMs with GPU accelerated graphics." = "Il miglior Renderer per questo dispositivo verrà selezionato automaticamente. Puoi sceglierne un altro con questa opzione. Si applica solo alle VM QEMU con accelerazione GPU";

/* No comment provided by engineer. */
"If set, a frame limit can improve smoothness in rendering by preventing stutters when set to the lowest value your device can handle." = "Se impostato, un limite di frame può migliorare l'esperienza quando impostato al valore minimo di refresh del proprio dispositivo.";

/* No comment provided by engineer. */
"By default, the best backend for the target will be used. If the selected backend is not available for any reason, an alternative will automatically be selected." = "Il miglior backand disponibile verrà scelto automaticamente. Se il backend selezionato non fosse disponibile, verrà selezionata un'alternativa automaticamente";

/* No comment provided by engineer. */
"FPS Limit" = "Limite FPS";

/* No comment provided by engineer. */
"QEMU Sound" = "Suono QEMU";

/* No comment provided by engineer. */
"Renderer Backend" = "Backend Renderer";

/* No comment provided by engineer. */
"Sound Backend" = "Backend Suono";

/* No comment provided by engineer. */
"Mouse/Keyboard" = "Mouse/Tastiera";

/* No comment provided by engineer. */
"QEMU Pointer" = "Puntatore QEMU";

/* No comment provided by engineer. */
"QEMU Keyboard" = "Tastiera QEMU";

/* No comment provided by engineer. */
"Capture input automatically when entering full screen" = "Cattura automaticamente l'input in modalità schermo intero";

/* No comment provided by engineer. */
"If enabled, input capture will toggle automatically when entering and exiting full screen mode." = "Se abilitato, la cattura dell'input verrà attivata e disattivata all'entrata e all'uscita dalla modalità a schermo intero";

/* No comment provided by engineer. */
"Capture input automatically when window is focused" = "Cattura automaticamente l'input quando la finestra è in primo piano";

/* No comment provided by engineer. */
"If enabled, input capture will toggle automatically when the VM's window is focused." = "Se abilitato, la cattura dell'imput verrà automaticamente attivata o disattivata quando la finestra della VM è in primo piano o non selezionata";

/* No comment provided by engineer. */
"Option (⌥) is Meta key" = "Option (⌥) come Meta key";

/* No comment provided by engineer. */
"If enabled, Option will be mapped to the Meta key which can be useful for emacs. Otherwise, option will work as the system intended (such as for entering international text)." = "Se abilitata, il tasto Option (⌥) verrà mappato come Meta key, utile per emacs. In caso contrario, il tasto Option funzionerà come da impostazioni di sistema (ad esempio per inserimento di testo speciale o con caratteri internazionali)";

/* No comment provided by engineer. */
"Num Lock is forced on" = "Forza attivazione Num Lock";

/* No comment provided by engineer. */
"Sound Backend" = "Mostra icona nella barra dei menu";

/* No comment provided by engineer. */
"Legacy" = "Legacy";

/* No comment provided by engineer. */
"Legacy (PS/2) Mode" = "Modalità Legacy (PS/2)";

/* No comment provided by engineer. */
"License" = "Licenza";

/* UTMQemuConstants */
"Linear" = "Lineare";

/* UTMAppleConfigurationBoot */
"Linux" = "Linux";

/* UTMLegacyQemuConfiguration
   UTMQemuConstants */
"Linux Device Tree Binary" = "Albero Binario dei Dispositivi Linux";

/* No comment provided by engineer. */
"Linux initial ramdisk:" = "RAMDisk iniziale di Linux:";

/* No comment provided by engineer. */
"Linux initial ramdisk (optional)" = "RAMDisk iniziale di Linux (opzionale)";

/* UTMLegacyQemuConfiguration
   UTMQemuConstants */
"Linux Kernel" = "Kernel Linux";

/* No comment provided by engineer. */
"Linux kernel (required)" = "Kernel Linux (richiesto)";

/* UTMLegacyQemuConfiguration
   UTMQemuConstants */
"Linux RAM Disk" = "RAMDisk di Linux";

/* No comment provided by engineer. */
"Linux Root FS Image (optional)" = "Immagine del File System Root di Linux";

/* No comment provided by engineer. */
"Linux Settings" = "Impostazioni Linux";

/* No comment provided by engineer. */
"Logging" = "Logging";

/* No comment provided by engineer. */
"MAC Address" = "MAC Address";

/* No comment provided by engineer. */
"Machine" = "Macchina";

/* UTMAppleConfigurationBoot */
"macOS" = "macOS";

/* VMWizardOSMacView */
"macOS guests are only supported on ARM64 devices." = "I guest macOS sono supportati solo dai dispositivi ARM64";

/* VMWizardState */
"macOS is not supported with QEMU." = "macOS non è supportato da QEMU.";

/* No comment provided by engineer. */
"macOS Settings" = "Impostazioni macOS";

/* UTMQemuManager */
"Manager being deallocated, killing pending RPC." = "Manager in deallocazione, arresto degli RPC in attesa.";

/* UTMQemuConstants */
"Manual Serial Device (advanced)" = "Dispositivo Seriale Manuale (avanzate)";

/* No comment provided by engineer. */
"Maximum Shared USB Devices" = "Dispositivi USB Condivisi Massimi";

/* No comment provided by engineer. */
"MiB" = "MiB";

/* No comment provided by engineer. */
"Memory" = "Memoria";

/* VMDisplayMetalWindowController */
"Metal is not supported on this device. Cannot render display." = "Metal non è supportato da questo dispositivo. Impossibile mostrare il display.";

/* No comment provided by engineer. */
"Minimum size: %@" = "Dimensione Minima: %@";

/* No comment provided by engineer. */
"Mode" = "Modalità";

/* No comment provided by engineer. */
"Modify settings for this VM." = "Modifica le impostazioni di questa VM";

/* UTMAppleConfigurationDevices */
"Mouse" = "Mouse";

/* No comment provided by engineer. */
"Mouse Wheel" = "Rotella del Mouse";

/* No comment provided by engineer. */
"Move…" = "Sposta...";

/* No comment provided by engineer. */
"Move this VM from internal storage to elsewhere." = "Sposta questa VM dalla memoria di archiviazione interna ad altrove.";

/* No comment provided by engineer. */
"Move Up" = "Su";

/* No comment provided by engineer. */
"Move Down" = "Giù";

/* No comment provided by engineer. */
"Move selected VM" = "Sposta la VM selezionata";

/* Save VM overlay */
"Moving %@…" = "Spostamento di %@...";

/* UTMQemuConstants */
"MTD (NAND/NOR)" = "MTD (NAND/NOR)";

/* No comment provided by engineer. */
"Name" = "Nome";

/* VMConfigInfoView */
"Name is an invalid filename." = "Il nome non è un nome di file valido.";

/* UTMQemuConstants */
"Nearest Neighbor" = "Nearest Neighbor";

/* No comment provided by engineer. */
"Network" = "Rete";

/* No comment provided by engineer. */
"Network Mode" = "Modalità di Rete";

/* No comment provided by engineer. */
"New" = "Crea";

/* No comment provided by engineer. */
"New…" = "Crea...";

/* No comment provided by engineer. */
"New Drive…" = "Nuovo Disco...";

/* No comment provided by engineer. */
"New from template…" = "Nuovo da template...";

/* VMConfigPortForwardingViewController */
"New port forward" = "Nuovo Inoltro di Porta";

/* No comment provided by engineer. */
"New Virtual Machine" = "Nuova Macchina Virtuale";

/* No comment provided by engineer. */
"New VM" = "Nuova VM";

/* Clone VM name prompt message */
"New VM name" = "Nuovo nome della VM";

/* No comment provided by engineer. */
"No" = "No";

/* UTMQemuManager */
"No connection for RPC." = "Nessuna connessione per RPC.";

/* VMConfigExistingViewController */
"No debug log found!" = "Nessun messaggio di debug trovato!";

/* No comment provided by engineer. */
"No drives added." = "Nessun disco aggiunto.";

/* VMDisplayWindowController */
"No drives connected." = "Nessun disco connesso.";

/* UTMDownloadSupportToolsTaskError */
"No empty removable drive found. Make sure you have at least one removable drive that is not in use." = "Nessun disco rimovibile trovato. Assicurati di avere almeno un disco rimovibile attualmente non in uso";

/* UTMData */
"No log found!" = "Nessun messaggio di log trovato!";

/* No comment provided by engineer. */
"No output device is selected for this window." = "Nessun dispositivo di output selezionato per questa finestra.";

/* VMQemuDisplayMetalWindowController */
"No USB devices detected." = "Nessun dispositivo USB rilevato";

/* VMToolbarDriveMenuView */
"none" = "nessuno";

/* UTMLegacyQemuConfiguration
   UTMQemuConstants */
"None" = "Nessuno/a";

/* UTMQemuConstants */
"None (Advanced)" = "Nessuno (Avanzato)";

/* No comment provided by engineer. */
"Not running" = "Non in essecuzione";

/* No comment provided by engineer. */
"Note: Boot order is as listed." = "Nota: La priorità di avvio è come indicata.";

/* No comment provided by engineer. */
"Note: select the path to share from the main screen." = "Nota: seleziona il percorso da condividere dall'interfaccia principale.";

/* No comment provided by engineer. */
"Note: Shared directories will not be saved and will be reset when UTM quits." = "Nota: Le cartelle condivise non saranno salvate e verranno svuotate all'uscita da UTM.";

/* No comment provided by engineer. */
"Notes" = "Note";

/* UTMQemuConstants */
"NVMe" = "NVMe";

/* VMDisplayWindowController */
"OK" = "OK";

/* No comment provided by engineer. */
"Only available if host architecture matches the target. Otherwise, TCG emulation is used." = "Disponibile solo se l'architettura dell'host combacia con la destinazione. In alternativa, l'emulazione TCG verrà utilizzata.";

/* No comment provided by engineer. */
"Open VM Settings" = "Apri la configurazione della VM";

/* No comment provided by engineer. */
"Open…" = "Apri...";

/* No comment provided by engineer. */
"Operating System" = "Sistema operativo";

/* No comment provided by engineer. */
"Optionally select a directory to make accessible inside the VM. Note that support for shared directories varies by the guest operating system and may require additional guest drivers to be installed. See UTM support pages for more details." = "Se vuoi, puoi selezionare una cartella da rendere accessibile all'interno della VM. Il supporto alle cartelle condivise varia in base al sistema operativo e potrebbe essere necessario installare driver sulla VM. Controlla le pagine di supporto di UTM per ulteriori informazioni.";

/* No comment provided by engineer. */
"Skip ISO boot" = "Salta avvio da ISO";
"Other" = "Altro";
"Options" = "Opzioni";
"Legacy Hardware" = "Hardware Legacy";
"If checked, emulated devices with higher compatibility will be instantiated at the cost of performance." = "Se attivo, i dispositivi emulati avranno una compatibilità elevata, al costo di prestazioni peggiori.";

/* No comment provided by engineer. */
"Path" = "Percorso";

/* VMDisplayWindowController */
"Pause" = "Pausa";

/* UTMVirtualMachine */
"Paused" = "In Pausa";

/* UTMVirtualMachine */
"Pausing" = "In Pausa...";

/* UTMQemuConstants */
"PC System Flash" = "Flash del Sistema PC";

/* No comment provided by engineer. */
"Pending" = "In attesa";

/* VMDisplayWindowController */
"Play" = "Avvia";

/* VMWizardState */
"Please select a boot image." = "Seleziona l'immagine di avvio.";

/* VMWizardState */
"Please select a kernel file." = "Seleziona un file del kernel.";

/* No comment provided by engineer. */
"Please select a macOS recovery IPSW." = "Seleziona un archivo IPSW di recupero di macOS.";

/* VMWizardState */
"Please select a system to emulate." = "Seleziona un sistema da emulare.";

/* No comment provided by engineer. */
"Please select an uncompressed Linux kernel image." = "Seleziona un'immagine non compressa del kernel Linux.";

/* No comment provided by engineer. */
"Port Forward" = "Inoltro di Porta";

/* UTMJSONStream */
"Port is not connected." = "Porta non connessa";

/* No comment provided by engineer. */
"Power Off" = "Spegni";

/* No comment provided by engineer. */
"Preconfigured" = "Preconfigurato";

/* No comment provided by engineer. */
"Protocol" = "Protocollo";

/* A download process is about to begin. */
"Preparing…" = "Preparazione…";

/* VMDisplayQemuMetalWindowController */
"Press %@ to release cursor" = "Premi %@ per rilasciare il cursore";

/* UTMQemuConstants */
"Pseudo-TTY Device" = "Dispositivo pseudo-TTY";

/* No comment provided by engineer. */
"PS/2 has higher compatibility with older operating systems but does not support custom cursor settings." = "PS/2 ha una compatibilità superiore con i sistemi operativi più vecchi, ma non supporta le impostazioni specifiche del cursore.";

/* No comment provided by engineer. */
"QEMU" = "QEMU";

/* No comment provided by engineer. */
"QEMU Arguments" = "Argomenti QEMU";

/* UTMQemuVirtualMachine */
"QEMU exited from an error: %@" = "QEMU si è interrotto a causa di un errore: %@";

/* No comment provided by engineer. */
"QEMU Machine Properties" = "Proprietà della macchina QEMU";

/* UTMQemuConstants */
"QEMU Monitor (HMP)" = "Monitor QEMU (HMP)";

/* VMDisplayWindowController */
"Querying drives status..." = "Verifica dello stato dei dischi…";

/* VMQemuDisplayMetalWindowController */
"Querying USB devices..." = "Verifica dello stato dei dispositivi USB…";

/* No comment provided by engineer. */
"Quit" = "Esci";

/* No comment provided by engineer. */
"Terminate UTM and stop all running VMs." = "Termina UTM e tutte le macchine virtuali in esecuzione.";

/* VMQemuDisplayMetalWindowController */
"Quitting UTM will kill all running VMs." = "L'uscita da UTM causerà l'interruzione di tutte le VM in esecuzione.";

/* No comment provided by engineer. */
"RAM" = "RAM";

/* No comment provided by engineer. */
"Random" = "Casuale";

/* No comment provided by engineer. */
"Raw Image" = "Immagine Raw";

/* No comment provided by engineer. */
"Advanced. If checked, a raw disk image is used. Raw disk image does not support snapshots and will not dynamically expand in size." = "Impostazione Avanzata. Se attivo, verrà utilizzata un'immagine disco raw. Le immagini disco raw non supportano le snapshot e non vengono ridimensionate automaticamente.";

/* VMDisplayAppleController */
"Read Only" = "Sola Lettura";

/* No comment provided by engineer. */
"Reclaim" = "Recupera";

/* No comment provided by engineer. */
"Reclaim Space" = "Recupera Spazio";

/* No comment provided by engineer. */
"Reclaim disk space by re-converting the disk image." = "Recupera spazio su disco riconvertendo l'immagine disco.";

/* No comment provided by engineer. */
"Compress" = "Comprimi";

/* No comment provided by engineer. */
"Compress by re-converting the disk image and compressing the data." = "Recupera spazio su disco riconvertendo l'immagine disco e comprimendo i dati.";

/* No comment provided by engineer. */
"Resize" = "Ridimensiona";

/* No comment provided by engineer. */
"Increase the size of the disk image." = "Incrementa la dimensione dell'immagine disco.";

/* UTMQemuConstants */
"Regular" = "Normale";

/* No comment provided by engineer. */
"Removable" = "Rimovibile";

/* No comment provided by engineer. */
"If checked, the drive image will be stored with the VM." = "Se attivo, l'immagine del disco sarà salvata insieme alla VM";

/* No comment provided by engineer. */
"If checked, no drive image will be stored with the VM. Instead you can mount/unmount image while the VM is running." = "Se attivo, l'immagine del disco non sarà salvata insieme alla VM. Può essere, invece, montata e smontata quando la VM è in esecuzione";

/* No comment provided by engineer. */
"Removable Drive" = "Disco Rimovibile";

/* No comment provided by engineer. */
"Remove" = "Elimina";

/* VMDisplayAppleController */
"Remove…" = "Elimina…";

/* VMDisplayQemuDisplayController */
"Request power down" = "Richiedi Spegnimento";

/* No comment provided by engineer. */
"Requires SPICE guest agent tools to be installed." = "Richiede che gli strumenti guest di SPICE siano installati.";

/* No comment provided by engineer. */
"Requires SPICE guest agent tools to be installed. Retina Mode is recommended only if the guest OS supports HiDPI." = "Richiede che gli strumenti guest di SPICE siano installati. La Modalità Retina è raccomandata solo sui sistemi operativi che supportano HiDPI.";

/* No comment provided by engineer. */
"Requires SPICE WebDAV service to be installed." = "Richiede l'installazione del servizio WebDAV di SPICE.";

/* No comment provided by engineer. */
"Reset" = "Reset";

/* No comment provided by engineer. */
"Resize" = "Ridimensiona";

/* No comment provided by engineer. */
"Resize Console Command" = "Ridimensiona i Comandi della Console";

/* No comment provided by engineer. */
"Resize display to screen size and orientation automatically" = "Ridimensiona e orienta il display in base alle dimensioni dello schermo.";

/* No comment provided by engineer. */
"Resize display to window size automatically" = "Ridimensiona e orienta il display in base alla finestra";

/* No comment provided by engineer. */
"Resizing is experimental and could result in data loss. You are strongly encouraged to back-up this VM before proceeding. Would you like to resize to %@ GiB?" = "Il ridimensionamento è una funzionalità sperimentale e potrebbe causare perdita di dati. Sei incoraggiato ad effettuare un backup di questa VM prima di procedere. Ridimensionare a %@ GiB?";

/* No comment provided by engineer. */
"Resolution" = "Risoluzione";

/* No comment provided by engineer. */
"Width" = "Larghezza";

/* No comment provided by engineer. */
"Height" = "Altezza";

/* No comment provided by engineer. */
"Only available on macOS virtual machines." = "Disponibile solo su macchine virtuali macOS.";

/* No comment provided by engineer. */
"Dynamic Resolution" = "Risoluzione Dinamica";

/* No comment provided by engineer. */
"Only available on macOS 14+ virtual machines." = "Disponibile solo su macchine virtuali macOS (versione 14 o superiore).";

/* No comment provided by engineer. */
"Restart" = "Ricomincia";

/* UTMVirtualMachine */
"Resuming" = "Ripresa";

/* No comment provided by engineer. */
"Retina Mode" = "Modalità Retina";

/* No comment provided by engineer. */
"Reveal where the VM is stored." = "Mostra la posizione della VM nel file system.";

/* UTMAppleConfiguration */
"Rosetta is not supported on the current host machine." = "Rosetta non è supportato dalla macchina host attuale.";

/* No comment provided by engineer. */
"Root Image" = "Radice Immagine";

/* No comment provided by engineer. */
"RNG Device" = "Dispositivo RNG";

/* No comment provided by engineer. */
"Run" = "Avvia";

/* No comment provided by engineer. */
"Run without saving changes" = "Avvia senza applicare le modifiche";

/* No comment provided by engineer. */
"Run Recovery" = "Avvia Ripristino";

/* No comment provided by engineer. */
"Run selected VM" = "Avvia VM selezionata";

/* No comment provided by engineer. */
"Run the VM in the foreground." = "Avvia la VM in primo piano";

/* No comment provided by engineer. */
"Run the VM in the foreground, without saving data changes to disk." = "Avvia la VM in primo piano, senza applicare le modifiche su disco.";

/* No comment provided by engineer. */
"Running" = "In esecuzione";

/* No comment provided by engineer. */
"Running low on memory! UTM might soon be killed by iOS. You can prevent this by decreasing the amount of memory and/or JIT cache assigned to this VM" = "Poca memoria a disposizione! UTM potrebbe presto essere interrotto da iOS. Puoi prevenire che questo accada diminuendo la quantità di memoria e/o della cache JIT di questa VM";

/* No comment provided by engineer. */
"Save" = "Salva";

/* Save VM overlay */
"Saving %@…" = "Salvataggio di %@...";

/* No comment provided by engineer. */
"Selected:" = "Selezionato:";

/* No comment provided by engineer. */
"Set to 0 for default which is 1/4 of the allocated Memory size. This is in addition to the host memory!" = "Impostato al valore predefinito di 0, che corrisponde a un quarto della memoria allocata. Questa si aggiunge alla quantità di memoria utilizzata!";

/* No comment provided by engineer. */
"Set to 0 to use maximum supported CPUs. Force multicore might result in incorrect emulation." = "Imposta a 0 per utilizzare il numero massimo di CPU supportate. Forzare l'uso di multicore potrebbe risultare in emulazione scorretta.";

/* UTMQemuConstants */
"SCSI" = "SCSI";

/* UTMQemuConstants */
"SD Card" = "Scheda SD";

/* No comment provided by engineer. */
"Select a file." = "Seleziona un file.";

/* VMDisplayWindowController */
"Select Drive Image" = "Seleziona un'Immagine Disco";

/* VMDisplayAppleWindowController
   VMDisplayWindowController */
"Select Shared Folder" = "Seleziona una Cartella Condivisa";

/* SavePanel */
"Select where to export QEMU command:" = "Seleziona dove esportare il comando QEMU:";

/* SavePanel */
"Select where to save debug log:" = "Seleziona dove salvare i messaggi di log:";

/* SavePanel */
"Select where to save UTM Virtual Machine:" = "Seleziona dove salvare la Macchina Virtuale UTM:";

/* No comment provided by engineer. */
"Selected:" = "Selezionato:";

/* VMDisplayQemuDisplayController */
"Sends power down request to the guest. This simulates pressing the power button on a PC." = "Invia la richiesta di spegnimento al Guest. Questo simula la pressione del tasto di accensione su un PC.";

/* No comment provided by engineer. */
"Serial" = "Seriale";

/* VMDisplayAppleWindowController
   VMDisplayQemuDisplayController */
"Serial %lld" = "Seriale %lld";

/* No comment provided by engineer. */
"Server Address" = "Indirizzo del Server";

/* No comment provided by engineer. */
"Settings" = "Impostazioni";

/* Share context menu */
"Share" = "Condividi";

/* Share context menu */
"Share…" = "Condividi...";

/* No comment provided by engineer. */
"Share a copy of this VM and all its data." = "Condividi una copia di questa VM e di tutti i suoi dati.";

/* No comment provided by engineer. */
"Share Directory" = "Condividi Cartella";

/* No comment provided by engineer. */
"Share is read only" = "Condivisione in sola lettura";

/* No comment provided by engineer. */
"Share USB devices from host" = "Condividi dispositivi USB dall'host";

/* No comment provided by engineer. */
"Shared Directory" = "Cartella Condivisa";

/* VMConfigAppleSharingView */
"Shared directories in macOS VMs are only available in macOS 13 and later." = "Le cartelle condivise nella VM macOS sono disponibili da macOS 13 alle versioni successive.";

/* UTMQemuConstants */
"Shared Network" = "Rete Condivisa";

/* VMConfigSharingViewController */
"Shared path has moved. Please re-choose." = "Il percorso condiviso è cambiato. Sceglierne uno nuovo.";

/* VMConfigSharingViewController */
"Shared path is no longer valid. Please re-choose." = "Il percorso condiviso non è più valido. Sceglierne uno nuovo.";

/* No comment provided by engineer. */
"Share selected VM" = "Condividi la VM selezionata";

/* No comment provided by engineer. */
"Sharing" = "Condivisione";

/* No comment provided by engineer. */
"Show Advanced Settings" = "Mostra Impostazioni Avanzate";

/* No comment provided by engineer. */
"Show All…" = "Mostra Tutto...";

/* No comment provided by engineer. */
"Show in Finder" = "Mostra nel Finder";

/* No comment provided by engineer. */
"Should be off for older operating systems such as Windows 7 or lower." = "Dovrebbe essere disattivato per sistemi operativi come Windows 7 o precedenti.";

/* No comment provided by engineer. */
"Should be on always unless the guest cannot boot because of this." = "Dovrebbe essere attivo, a meno che il sistema operativo Guest abbia problemi di avvio a causa di questa opzione.";

/* No comment provided by engineer. */
"Size" = "Dimensione";

/* No comment provided by engineer. */
"Skip Boot Image" = "Salta Immagine di Avvio";

/* New VM window. */
"Skip ISO boot" = "Salta Avvio da ISO";

/* No comment provided by engineer. */
"Skip ISO boot (advanced)" = "Salta Avvio da ISO (Avanzato)";

/* No comment provided by engineer. */
"Slower, but can run other CPU architectures." = "Più lento, ma in grado di eseguire più architetture di CPU.";

/* No comment provided by engineer. */
"Sound" = "Audio";

/* New VM window. */
"Some older systems do not support UEFI boot, such as Windows 7 and below." = "Alcuni sistemi non supportano l'avvio UEFI, come Windows 7 e precedenti.";

/* No comment provided by engineer. */
"Specify the size of the drive where data will be stored into." = "Specifica la dimensione del disco dove salvare i dati.";

/* UTMQemuConstants */
"SPICE WebDAV" = "SPICE WebDAV";

/* No comment provided by engineer. */
"Start" = "Inizia";

/* UTMVirtualMachine */
"Started" = "Avviata";

/* UTMVirtualMachine */
"Starting" = "In Avvio";

/* No comment provided by engineer. */
"Stop" = "Arresta";

/* No comment provided by engineer. */
"Stop the running VM." = "Arresta la VM in esecuzione.";

/* No comment provided by engineer. */
"Stop selected VM" = "Arresta la VM selezionata.";

/* No comment provided by engineer. */
"Stop…" = "Arresta...";

/* UTMVirtualMachine */
"Stopped" = "Arrestata";

/* UTMVirtualMachine */
"Stopping" = "In Arresto";

/* No comment provided by engineer. */
"Storage" = "Archiviazione";

/* No comment provided by engineer. */
"stty cols $COLS rows $ROWS\n" = "stty cols $COLS rows $ROWS\n";

/* No comment provided by engineer. */
"Style" = "Stile";

/* No comment provided by engineer. */
"Summary" = "Sommario";

/* Welcome view */
"Support" = "Supporto";

/* UTMVirtualMachine */
"Suspended" = "Sospesa";

/* No comment provided by engineer. */
"Status" = "Stato";

/* No comment provided by engineer. */
"System" = "Sistema";

/* No comment provided by engineer. */
"Target" = "Destinazione";

/* UTMQemuConstants */
"TCP" = "TCP";

/* UTMQemuConstants */
"TCP Client Connection" = "Connessione del Client TCP";

/* VMConfigPortForwardingViewController */
"TCP Forward" = "Inoltro TCP";

/* UTMQemuConstants */
"TCP Server Connection" = "Connessione del Server TCP";

/* No comment provided by engineer. */
"Test" = "Test";

/* No comment provided by engineer. */
"Text Color" = "Colore del Testo";

/* VMDisplayQemuDisplayController */
"Tells the VM process to shut down with risk of data corruption. This simulates holding down the power button on a PC." = "Informa il processo della VM di arrestarsi con il rischio di compromettere dei dati. Questo simula la pressione a lungo del tasto di accesione di un PC";

/* SizeTextField */
"The amount of storage to allocate for this image. Ignored if importing an image. If this is a raw image, then an empty file of this size will be stored with the VM. Otherwise, the disk image will dynamically expand up to this size." = "La quantità di spazio da allocare per questa immagine. Ignorato se l'immagine viene importata. Se questa è un'immagine raw, un file vuoto con questa dimensione sarà salvato con la VM. In alternativa, l'immagine disco si espanderà fino alla dimensione espressa qui.";

/* SizeTextField */
"The amount of storage to allocate for this image. An empty file of this size will be stored with the VM." = "La quantità di spazio da allocare per questa immagine. Un file vuoto di questa dimensione verrà salvato con la VM.";

/* UTMConfiguration */
"The backend for this configuration is not supported." = "Il backend scelto per questa configurazione non è supportato.";

/* UTMConfiguration */
"The drive '%@' already exists and cannot be created." = "Il disco “%@” esiste già e non può essere creato.";

/* UTMDownloadSupportToolsTaskError */
"The guest support tools have already been mounted." = "Gli strumenti di supporto del guest sono stati già montati.";

/* UTMAppleConfiguration */
"The host operating system needs to be updated to support one or more features requested by the guest." = "Il sistema operativo host deve essere aggiornato per supportare una o più funzionalità richieste dal guest.";

/* No comment provided by engineer. */
"The selected architecture is unsupported in this version of UTM." = "L'architettura selezionata non è supportata da questa versione di UTM.";

/* VMWizardState */
"The selected boot image contains the word '%@' but the guest architecture is '%@'. Please ensure you have selected an image that is compatible with '%@'." = "L'immagine di avvio contiene la parola “%1$@“, mentre l'architettura guest è “%2$@“. Assicurati di aver selezionato un'immagine compatibile con “%3$@“.";

/* VMConfigSystemViewController */
"The total memory usage is close to your device's limit. iOS will kill the VM if it consumes too much memory." = "L'uso di memoria attuale è vicino al limite massimo del tuo dispositivo, iOS terminerà la VM se consuma troppa memoria.";

/* No comment provided by engineer. */
"The target does not support hardware emulated serial connections." = "La destinazione non supporta le connessioni seriali emulate.";

/* UTMQemuVirtualMachine */
"The virtual machine is in an invalid state." = "La macchina virtuale è in uno stato non valido.";

/* No comment provided by engineer. */
"Theme" = "Tema";

/* Error shown when importing a ZIP file from web that doesn't contain a UTM Virtual Machine. */
"There is no UTM file in the downloaded ZIP archive." = "Non è presente un file UTM nell'archivio ZIP scaricato.";

/* No comment provided by engineer. */
"These are advanced settings affecting QEMU which should be kept default unless you are running into issues." = "Queste sono configurazioni avanzate che hanno impatto su QEMU e dovrebbero essere lasciate alle impostazioni predefinite a meno che non causino problemi.";

/* No comment provided by engineer. */
"These settings are unavailable in console display mode." = "Queste impostazioni non sono disponibili nella modalità console";

/* No comment provided by engineer. */
"This build does not emulation." = "Questa build non supporta l'emulazione.";

/* UTMQemuVirtualMachine */
"This build of UTM does not support emulating the architecture of this VM." = "Questa build di UTM non supporta l'emulazione dell'architettura di questa VM.";

/* VMConfigSystemView */
"This change will reset all settings" = "Questa modifica riporterà alle impostazioni predefinite";

/* UTMConfiguration */
"This configuration is saved with a newer version of UTM and is not compatible with this version." = "Questa configurazione è stata salvata con una versione più recente di UTM e non è compatibile con la versione attuale";

/* UTMConfiguration */
"This configuration is too old and is not supported." = "Questa configurazione proviene da una versione precedente non più supportata.";

/* VMConfigAppleSharingView */
"This directory is already being shared." = "Questa cartella è già condivisa.";

/* UTMQemuSystem */
"This version of macOS does not support audio in console mode. Please change the VM configuration or upgrade macOS." = "Questa versione di macOS non supporta l'audio in modalità console. Cambia la configurazione della VM o aggiorna macOS.";

/* UTMQemuSystem */
"This version of macOS does not support GPU acceleration. Please change the VM configuration or upgrade macOS." = "Questa versione di macOS non supporta l'accelerazione della GPU. Cambia la configurazione della VM o aggiorna macOS.";

/* No comment provided by engineer. */
"This is appended to the -machine argument." = "Aggiunto all'argomento -machine";

/* UTMAppleConfiguration */
"This is not a valid Apple Virtualization configuration." = "Questa non è una configurazione valida per la Virtualizzazione Apple.";

/* VMDisplayWindowController */
"This may corrupt the VM and any unsaved changes will be lost. To quit safely, shut down from the guest." = "Questo potrebbe compromettere la VM e ogni modifica non salvata verrà persa. Per uscire in sicurezza, spegni la macchina dal sistema guest.";

/* No comment provided by engineer. */
"This operating system is unsupported on your machine." = "Questo sistema operativo non è supportato dalla tua macchina.";

/* UTMDataExtension */
"This virtual machine cannot be run on this machine." = "Questo macchina virtuale non è supportato dalla tua macchina.";

/* UTMAppleConfiguration */
"This virtual machine cannot run on the current host machine." = "Questo macchina virtuale non è supportato dalla tua macchina host attuale.";

/* UTMAppleConfiguration */
"This virtual machine contains an invalid hardware model. The configuration may be corrupted or is outdated." = "Questa macchina virtuale contiene un modello hardware non valido. La configurazione potrebbe essere non aggiornata o non corretta.";

/* No comment provided by engineer. */
"This virtual machine has been removed." = "Questa macchina virtuale è stata eliminata.";

/* VMDisplayWindowController */
"This will reset the VM and any unsaved state will be lost." = "Questo riavvierà la VM e ogni modifica non salvata sarà persa.";

/* UTMQemuManager */
"Timed out waiting for RPC." = "Timeout di RPC.";

/* VMDisplayAppleWindowController */
"To access the shared directory, the guest OS must have Virtiofs drivers installed. You can then run `sudo mount -t virtiofs share /path/to/share` to mount to the share path." = "Per accedere alla cartella condivisa, il sistema operativo guest necessita dei driver Virtiofs. Succesivamente, puoi eseguire `sudo mount -t virtiofs share /path/to/share` per montare la cartella condivisa.";

/* VMMetalView */
"To capture input or to release the capture, press Command and Option at the same time." = "Per catturare o rilasciare la cattura dell'input, premi Command e Option contemporaneamente.";

/* No comment provided by engineer. */
"To install macOS, you need to download a recovery IPSW. If you do not select an existing IPSW, the latest macOS IPSW will be downloaded from Apple." = "Per installare macOS, avrai bisogno di scaricare un file IPSW per il ripristino. Se non selezioni un file IPSW esistente, verrà scaricata da Apple l'ultima versione del file di ripristino IPSW di macOS.";

/* VMDisplayQemuMetalWindowController */
"To release the mouse cursor, press %@ at the same time." = "Per rilasciare il cursore, premi %@ contemporaneamente.";

/* UTMAppleConfigurationDevices */
"Trackpad" = "Trackpad";

/* No comment provided by engineer. */
"Tweaks" = "Ritocchi";

/* No comment provided by engineer. */
"Type" = "Tipo";

/* UTMQemuConstants */
"UDP" = "UDP";

/* VMConfigPortForwardingViewController */
"UDP Forward" = "Inoltro UDP";

/* No comment provided by engineer. */
"UEFI" = "UEFI";

/* No comment provided by engineer. */
"UEFI Boot" = "Avvio UEFI";

/* UTMQemuConfigurationError */
"UEFI is not supported with this architecture." = "L'avvio UEFI non è supportato da questa architettura.";

/* UTMData */
"Unable to add a shortcut to the new location." = "Impossibile aggiungere una scorciatoia alla nuova posizione.";

/* UTMUnavailableVirtualMachine */
"Unavailable" = "Non disponibile";

/* VMWizardState */
"Unavailable for this platform." = "Non disponibile per questa piattaforma.";

/* No comment provided by engineer. */
"Uncompressed Linux initial ramdisk (optional)" = "RAMDisk iniziale di Linux non compressa (opzionale))";

/* No comment provided by engineer. */
"Uncompressed Linux kernel (required)" = "Kernel du Linux non compresso (richiesto)";

/* UTMVirtualMachineExtension */
"Unknown" = "Sconosciuto";

/* No comment provided by engineer. */
"Upscaling" = "Upscaling";

/* UTMQemuConstants */
"USB" = "USB";

/* UTMQemuConstants */
"USB 2.0" = "USB 2.0";

/* UTMQemuConstants */
"USB 3.0 (XHCI)" = "USB 3.0 (XHCI)";

/* VMQemuDisplayMetalWindowController */
"USB Device" = "Dispositivo USB";

/* No comment provided by engineer. */
"USB Sharing" = "Condivisione USB";

/* No comment provided by engineer. */
"USB sharing not supported in this build of UTM." = "La condivisione USB non è supportata da questa versione di UTM.";

/* No comment provided by engineer. */
"USB Support" = "Supporto USB";

/* No comment provided by engineer. */
"Use Apple Virtualization" = "Usa la Virtualizzazione Apple";

/* No comment provided by engineer. */
"Use Command+Option (⌘+⌥) for input capture/release" = "Usa Command+Option (⌘+⌥) per catturare/rilasciare l'input";

/* No comment provided by engineer. */
"If disabled, the default combination Control+Option (⌃+⌥) will be used." = "Se disabilitato, verrà usata la combinazione di default Control+Option (⌃+⌥).";

/* No comment provided by engineer. */
"Use Hypervisor" = "Usa Hypervisor";

/* No comment provided by engineer. */
"Use TSO" = "Usa TSO";

/* No comment provided by engineer. */
"Only available when Hypervisor is used on supported hardware. TSO speeds up Intel emulation in the guest at the cost of decreased performance in general." = "Disponibile solo con un Hypervisor e hardware supportato. TSO incrementa le prestazioni di emulazione di un guest Intel, al costo di performance generali ridotte.";

/* No comment provided by engineer. */
"Use local time for base clock" = "Usa l'ora locale locale per l'orologio di sistema";

/* No comment provided by engineer. */
"Use Virtualization" = "Usa la Virtualizzazione";

/* Welcome view */
"User Guide" = "Guida Utente";

/* No comment provided by engineer. */
"VGA Device RAM (MB)" = "RAM del dispositivo VGA (MB)";

/* UTMQemuConstants */
"VirtFS" = "VirtFS";

/* UTMQemuConstants */
"VirtIO" = "VirtIO";

/* UTMConfigurationInfo
   UTMData */
"Virtual Machine" = "Macchina Virtuale";

/* No comment provided by engineer. */
"Virtual Machine Gallery" = "Libreria di Macchine Virtuali";

/* New VM window. */
"Virtualization Engine" = "Motore di Virtualizzazione";

/* No comment provided by engineer. */
"Virtualization is not supported on your system." = "La virtualizzazione non è supportata sul tuo sistema.";

/* No comment provided by engineer. */
"Virtualize" = "Virtualizza";

/* No comment provided by engineer. */
"VM display size is fixed" = "La dimensione del display della VM è costante";

/* UTMVirtualMachine+Sharing */
"VM frontend does not support shared directories." = "Il frontend della VM non supporta le cartelle condivise.";

/* No comment provided by engineer. */
"Waiting for VM to connect to display..." = "In attesa che la VM si colleghi al display…";

/* No comment provided by engineer. */
"Welcome to UTM" = "Benvenuto a UTM";

/* No comment provided by engineer. */
"WebDAV requires installing SPICE daemon. VirtFS requires installing device drivers." = "WebDAV richiede l'installazione del demone di sistema SPICE. VirtFS richiede l'installazione di driver.";

/* No comment provided by engineer. */
"Windows" = "Windows";

/* No comment provided by engineer. */
"Wait for Connection" = "Attesa della Connessione";

/* UTMDownloadSupportToolsTask */
"Windows Guest Support Tools" = "Strumenti di Supporto Guest per Windows";

/* VMQemuDisplayMetalWindowController */
"Would you like to connect '%@' to this virtual machine?" = "Vuoi collegare '%@' a questa macchina virtuale?";

/* VMDisplayAppleWindowController */
"Would you like to install macOS? If an existing operating system is already installed on the primary drive of this VM, then it will be erased." = "Vuoi installare macOS? Se un sistema operativo è già presente sul disco primario di questa VM, verrà eliminato.";

/* No comment provided by engineer. */
"Would you like to re-convert this disk image to reclaim unused space and apply compression? Note this will require enough temporary space to perform the conversion. Compression only applies to existing data and new data will still be written uncompressed. You are strongly encouraged to back-up this VM before proceeding." = "Vuoi ri-convertire questa immagine disco per recuperare spazio ed applicare la compressione? Nota che sarà necessaria la disponibilità di spazio su disco sufficiente per effettuare la conversione. La compressione si applica solo ai dati esistenti e i nuovi dati non verranno compressi. È fortemente consigliato fare un backup di questa VM prima di procedere.";

/* No comment provided by engineer. */
"Would you like to re-convert this disk image to reclaim unused space? Note this will require enough temporary space to perform the conversion. You are strongly encouraged to back-up this VM before proceeding." = "Vuoi ri-convertire questa immagine disco per recuperare spazio ed applicare la compressione? Nota che sarà necessaria la disponibilità di spazio su disco sufficiente per effettuare la conversione. È fortemente consigliato fare un backup di questa VM prima di procedere.";

/* No comment provided by engineer. */
"Yes" = "Sì";

/* VMConfigSystemView */
"Your device has %llu MB of memory and the estimated usage is %llu MB." = "Il tuo dispositivo ha %1$llu MB di memoria e l'uso stimato è di %2$llu MB.";

/* VMConfigAppleBootView
   VMWizardOSMacView */
"Your machine does not support running this IPSW." = "La tua macchina non supporta l'esecuzione di questo IPSW.";

/* ContentView */
"Your version of iOS does not support running VMs while unmodified. You must either run UTM while jailbroken or with a remote debugger attached. See https://getutm.app/install/ for more details." = "La tua versione di iOS non supporta l'esecuzione di VM senza modifiche. Dovrai eseguire UTM con debugger remoto o con un dispositivo jailbroken. Consulta https://getutm.app/install/ per maggiori informazioni.";

/* No comment provided by engineer. */
"Zoom" = "Zoom";

/* No comment provided by engineer. */
"Maintenance" = "Manutenzione";

/* No comment provided by engineer. */
"Reset UEFI Variables" = "Reimposta le Variabili UEFI";

/* No comment provided by engineer. */
"Options here only apply on next boot and are not saved." = "Queste impostazioni vengo applicate solo all'avvio successivo e non sono salvate.";

/* No comment provided by engineer. */
"You can use this if your boot options are corrupted or if you wish to re-enroll in the default keys for secure boot." = "Puoi usare questa opzione se le impostazioni di boot sono compromesse o se desideri ricreare le chiavi di default per il secure boot.";

/* No comment provided by engineer. */
"Create a new VM" = "Crea una nuova VM";

/* No comment provided by engineer. */
"Show UTM" = "Mostra UTM";

/* No comment provided by engineer. */
"Show the main window." = "Mostra la finestra principale.";

/* No comment provided by engineer. */
"Hide dock icon on next launch" = "Nascondi icona del dock al prossimo avvio";

/* No comment provided by engineer. */
"No virtual machines found." = "Nessuna macchina virtuale trovata.";

/* No comment provided by engineer. */
"Requires restarting UTM to take affect." = "Richiede il riavvio di UTM.";

/* No comment provided by engineer. */
"Choose" = "Seleziona";

/* No comment provided by engineer. */
"Arguments" = "Argomenti";

/* No comment provided by engineer. */
"Guest drivers are required for 3D acceleration." = "L'installazione dei driver guest è richiesta per l'accelerazione 3D.";

/* No comment provided by engineer. */
"GPU Acceleration supported" = "Accelerazione GPU Supportata";

/* No comment provided by engineer. */
"Automatic" = "Automatica";

/* No comment provided by engineer. */
"Read Only?" = "Sola Lettura";

/* No comment provided by engineer. */
"Update Interface" = "Aggiorna Interfaccia";

/* No comment provided by engineer. */
"Older versions of UTM added each IDE device to a separate bus. Check this to change the configuration to place two units on each bus." = "Le versioni precedenti di UTM aggiungevano ciascun dispositivo IDE ad un bus separato. Seleziona questa opzione per modificare la configurazione e impostare due unità su ciascun bus.";

/* No comment provided by engineer. */
"What's New" = "Novità di UTM";

/* No comment provided by engineer. */
"Enable UTM Server" = "Abilita UTM Server";

/* No comment provided by engineer. */
"Last Seen" = "Accesso più recente";

/* No comment provided by engineer. */
"Reset Identity" = "Ripristina Identità";

/* No comment provided by engineer. */
"Do you want to forget all clients and generate a new server identity? Any clients that previously paired with this server will be instructed to manually unpair with this server before they can connect again." = "Vuoi dimenticare tutti i client e generare una nuova identità del server? Verrà chiesto a tutti i client collegati di disassociarsi manualmente prima di poter stabile una nuova connessione.";

/* No comment provided by engineer. */
"Reset Identity" = "Ripristina Identità";

/* No comment provided by engineer. */
"Startup" = "Avvio";

/* No comment provided by engineer. */
"Automatically start UTM server" = "Avvia il Server UTM automaticamente";

/* No comment provided by engineer. */
"Reject unknown connections by default" = "Rifiuta connessioni sconosciute";

/* No comment provided by engineer. */
"If checked, you will not be prompted about any unknown connection and they will be rejected." = "Se attivo, non ti verrà chiesto di accettare richieste da origini sconosciute, che verranno automaticamente rifiutate.";

/* No comment provided by engineer. */
"Allow access from external clients" = "Permetti l'accesso da client esterni";

/* No comment provided by engineer. */
"By default, the server is only available on LAN but setting this will use UPnP/NAT-PMP to port forward to WAN." = "Di default, il server è solo disponibile su rete locale, attivando questa opzione verrà attivato UPnP/NAT-PMP per il port forwarding su WAN";

/* No comment provided by engineer. */
"Any" = "Qualunque";

/* No comment provided by engineer. */
"Specify a port number to listen on. This is required if external clients are permitted." = "Specifica una porta d'ascolto. Richiesto se l'accesso da client esterni è attivato.";

/* No comment provided by engineer. */
"Authentication" = "Autenticazione";

/* No comment provided by engineer. */
"Require Password" = "Richiedi Password";

/* No comment provided by engineer. */
"If enabled, clients must enter a password. This is required if you want to access the server externally." = "Se abilitato, i client dovranno inserire una password. Richiesto se vuoi accedere da client esterni.";
