/* A removable drive that has no image file inserted. */
"(empty)" = "(空)";

/* VMConfigAppleDriveDetailsView */
"(New Drive)" = "(新磁碟)";

/* No comment provided by engineer. */
"(new)" = "(新)";

/* VMData */
"(Unavailable)" = "(無法使用)";

/* QEMUConstant */
"%@ (%@)" = "%1$@ (%2$@)";

/* VMDisplayQemuDisplayController
VMToolbarDriveMenuView */
"%@ (%@): %@" = "%1$@ (%2$@): %3$@";

/* VMDisplayMetalWindowController */
"%@ (Display %lld)" = "%1$@ (顯示 %2$lld)";

/* VMDisplayAppleTerminalWindowController
VMDisplayQemuTerminalWindowController */
"%@ (Terminal %lld)" = "%1$@ (終端機 %2$lld)";

/* VMRemovableDrivesView */
"%@ %@" = "%1$@ %2$@";

/* No comment provided by engineer. */
"%@ ➡️ %@" = "%1$@ ➡️ %2$@";

/* VMDrivesSettingsView */
"%@ Drive" = "%@ 磁碟";

/* VMDrivesSettingsView */
"%@ Image" = "%@ 映像檔";

/* Format string for remaining time until a download finishes */
"%@ remaining" = "剩餘時間 %@";

/* Format string for the 'per second' part of a download speed. */
"%@/s" = "%@/秒";

/* Format string for download progress and speed, e. g. 5 MB of 6 GB (200 kbit/s) */
"%1$@ of %2$@ (%3$@)" = "%1$@ / %2$@ (%3$@)";

/* UTMDonateView */
"%d days" = "%d 日";

/* UTMDonateView */
"%d months" = "%d 月";

/* UTMDonateView */
"%d weeks" = "%d 週";

/* UTMDonateView */
"%d years" = "%d 年";

/* No comment provided by engineer. */
"• " = "• ";

/* UTMScriptingAppDelegate */
"A valid backend must be specified." = "必須指定有效的後端。";

/* UTMScriptingAppDelegate */
"A valid configuration must be specified." = "必須指定有效的設定。";

/* UTMAppleConfiguration */
"A valid kernel image must be specified." = "必須指定有效的核心映像檔。";

/* UTMScriptingAppDelegate */
"A valid UTM file must be specified." = "必須指定有效的 UTM 檔案。";

/* VMDisplayAppleController */
"Add…" = "新增⋯";

/* No comment provided by engineer. */
"Additional Options" = "額外選項";

/* No comment provided by engineer. */
"Additional Settings" = "額外設定";

/* VMConfigSystemView */
"Allocating too much memory will crash the VM." = "分配過多的記憶體會引致虛擬機故障。";

/* UTMData */
"AltJIT error: %@" = "AltJIT 錯誤：%@";

/* UTMData */
"An existing virtual machine already exists with this name." = "已有一個此名稱的虛擬機。";

/* UTMConfiguration */
"An internal error has occurred." = "發生內部錯誤。";

/* UTMConfiguration */
"An invalid value of '%@' is used in the configuration file." = "設定檔內使用了無效值「%@」。";

/* UTMRemoteSpiceVirtualMachine */
"An operation is already in progress." = "一項操作已進行中。";

/* UTMQemuImage */
"An unknown QEMU error has occurred." = "發生未知的 QEMU 錯誤。";

/* VMDisplayAppleDisplayController */
"An USB device containing the installer will be mounted in the virtual machine. Only macOS Sequoia (15.0) and newer guests are supported." = "包含安裝程式的 USB 裝置將裝載至虛擬機。只支援 macOS Sequoia (15.0) 和較新版本的客户端。";

/* No comment provided by engineer. */
"ANGLE (Metal)" = "ANGLE (Metal)";

/* No comment provided by engineer. */
"ANGLE (OpenGL)" = "ANGLE (OpenGL)";

/* VMConfigSystemView */
"Any unsaved changes will be lost." = "所有未儲存的變更都將遺失。";

/* No comment provided by engineer. */
"Approve" = "認可";

/* No comment provided by engineer. */
"Architecture" = "架構";

/* No comment provided by engineer. */
"Are you sure you want to exit UTM?" = "確定要離開 UTM 嗎？";

/* No comment provided by engineer. */
"Are you sure you want to permanently delete this disk image?" = "確定要永久刪除此磁碟映像檔嗎？";

/* No comment provided by engineer. */
"Are you sure you want to reset this VM? Any unsaved changes will be lost." = "確定要重設此虛擬機嗎？所有未儲存的變更都將遺失。";

/* No comment provided by engineer. */
"Are you sure you want to stop this VM and exit? Any unsaved changes will be lost." = "確定要停止此虛擬機並離開嗎？所有未儲存的變更都將遺失。";

/* No comment provided by engineer. */
"Authentication" = "認證";

/* No comment provided by engineer. */
"Automatic" = "自動";

/* UTMQemuConstants */
"Automatic Serial Device (max 4)" = "自動序列裝置 (最大值為 4)";

/* VMSessionState */
"Background task is about to expire" = "背景任務即將過期";

/* UTMLegacyQemuConfiguration
UTMQemuConstants */
"BIOS" = "BIOS";

/* No comment provided by engineer. */
"Block" = "封鎖";

/* No comment provided by engineer. */
"Blocked" = "已封鎖";

/* UTMQemuConstants */
"Bold" = "粗體";

/* No comment provided by engineer. */
"Boot" = "啟動";

/* No comment provided by engineer. */
"Boot Arguments" = "啟動引數";

/* No comment provided by engineer. */
"Boot Image Type" = "啟動映像檔種類";

/* No comment provided by engineer. */
"Boot IMG Image" = "啟動 IMG 映像檔";

/* No comment provided by engineer. */
"Boot ISO Image" = "啟動 ISO 映像檔";

/* No comment provided by engineer. */
"Boot ISO Image (optional)" = "啟動 ISO 映像檔 (可留空)";

/* No comment provided by engineer. */
"Boot VHDX Image" = "啟動 VHDX 映像檔 (可留空)";

/* UTMAppleConfigurationNetwork
UTMQemuConstants */
"Bridged (Advanced)" = "橋連 (進階)";

/* No comment provided by engineer. */
"Bridged Settings" = "橋連設定";

/* Welcome view */
"Browse UTM Gallery" = "瀏覽 UTM 虛擬機庫";

/* No comment provided by engineer. */
"Browse…" = "瀏覽⋯";

/* No comment provided by engineer. */
"Build" = "構建";

/* UTMAppleConfigurationTerminal
UTMQemuConstants */
"Built-in Terminal" = "內置終端機";

/* No comment provided by engineer. */
"Busy…" = "忙碌中⋯";

/* VMDisplayWindowController
VMQemuDisplayMetalWindowController */
"Cancel" = "取消";

/* UTMAppleVirtualMachine */
"Cannot access resource: %@" = "無法取用資源：%@";

/* UTMSWTPM */
"Cannot access TPM data." = "無法取用 TPM 資料。";

/* UTMAppleVirtualMachine */
"Cannot create virtual terminal." = "無法製作虛擬終端機。";

/* UTMData */
"Cannot find AltServer for JIT enable. You cannot run VMs until JIT is enabled." = "無法找到 JIT 啟用的 AltServer。在啟用 JIT 之前，你無法執行虛擬機。";

/* UTMRemoteServer */
"Cannot find VM with ID: %@" = "無法透過此 ID 找到虛擬機：";

/* UTMData */
"Cannot import this VM. Either the configuration is invalid, created in a newer version of UTM, or on a platform that is incompatible with this version of UTM." = "無法輸入此虛擬機。可能設定無效，或是在較新版本的 UTM 上製作，或是在與此版本的 UTM 不相容的平台上製作。";

/* UTMRemoteServer */
"Cannot reserve port %d for external access from NAT. Make sure no other device on the network has reserved it." = "無法為透過 NAT 的外部取用保留埠「%d」。請確定網絡上未有其他裝置保留它。";

/* No comment provided by engineer. */
"Caps Lock (⇪) is treated as a key" = "Caps Lock (⇪) 被視為按鍵";

/* VMMetalView */
"Capture Input" = "擷取輸入";

/* No comment provided by engineer. */
"Capture input automatically when entering full screen" = "進入全螢幕時自動擷取輸入";

/* No comment provided by engineer. */
"Capture input automatically when window is focused" = "視窗在焦點內時自動擷取輸入";

/* VMDisplayQemuMetalWindowController */
"Captured mouse" = "已擷取滑鼠";

/* Configuration boot device */
"CD/DVD" = "CD/DVD";

/* UTMLegacyQemuConfiguration
UTMQemuConstants */
"CD/DVD (ISO) Image" = "CD/DVD (ISO) 映像檔";

/* No comment provided by engineer. */
"CD/DVD Image" = "CD/DVD 映像檔";

/* VMDisplayWindowController */
"Change" = "變更";

/* VMDisplayAppleController */
"Change…" = "變更⋯";

/* No comment provided by engineer. */
"Choose" = "選擇";

/* No comment provided by engineer. */
"Clear" = "清除";

/* No comment provided by engineer. */
"Close" = "關閉";

/* VMQemuDisplayMetalWindowController */
"Closing this window will kill the VM." = "關閉此視窗會結束虛擬機。";

/* VMQemuDisplayMetalWindowController */
"Confirm" = "確認";

/* No comment provided by engineer. */
"Confirm Delete" = "確認刪除";

/* AppDelegate
VMDisplayWindowController */
"Confirmation" = "確認";

/* No comment provided by engineer. */
"Connect" = "連接";

/* No comment provided by engineer. */
"Connected" = "已連接";

/* No comment provided by engineer. */
"Connection" = "連線";

/* VMSessionState */
"Connection to the server was lost." = "與伺服器的連線中斷。";

/* No comment provided by engineer. */
"Console" = "主控台";

/* No comment provided by engineer. */
"Continue" = "繼續";

/* No comment provided by engineer. */
"CoreAudio (Output Only)" = "CoreAudio (只輸出)";

/* No comment provided by engineer. */
"Cores" = "核心";

/* No comment provided by engineer. */
"CPU" = "CPU";

/* No comment provided by engineer. */
"CPU Cores" = "CPU 核心";

/* No comment provided by engineer. */
"Create" = "製作";

/* No comment provided by engineer. */
"Create a new emulated machine from scratch." = "從頭開始製作新虛擬機。";

/* Welcome view */
"Create a New Virtual Machine" = "製作新虛擬機";

/* No comment provided by engineer. */
"Create a new virtual machine or import an existing one." = "製作新虛擬機或輸入一個現有的。";

/* VMConfigAppleDisplayView */
"Custom" = "自訂";

/* UTMSWTPM */
"Data not specified." = "未指定資料。";

/* UTMDonateView */
"day" = "日";

/* No comment provided by engineer. */
"Debug Logging" = "除錯記錄";

/* QEMUConstantGenerated
UTMQemuConstants */
"Default" = "預設";

/* VMWizardSummaryView */
"Default Cores" = "預設核心";

/* No comment provided by engineer. */
"Delete" = "刪除";

/* No comment provided by engineer. */
"Devices" = "裝置";

/* VMDisplayAppleWindowController */
"Directory sharing" = "目錄分享";

/* UTMAppleConfigurationDevices
UTMQemuConstants */
"Disabled" = "停用";

/* No comment provided by engineer. */
"Disconnect" = "中斷連接";

/* No comment provided by engineer. */
"Discovered" = "偵測到";

/* UTMLegacyQemuConfiguration
UTMQemuConstants */
"Disk Image" = "磁碟映像檔";

/* VMDisplayAppleWindowController */
"Display" = "顯示";

/* VMDisplayQemuDisplayController */
"Display %lld: %@" = "顯示 %1$lld：%2$@";

/* VMDisplayQemuDisplayController */
"Disposable Mode" = "即棄式模式";

/* No comment provided by engineer. */
"Do not save VM screenshot to disk" = "不儲存虛擬機的螢幕截圖至磁碟";

/* No comment provided by engineer. */
"Do not show confirmation when closing a running VM" = "關閉正在執行的虛擬機時不顯示確認";

/* No comment provided by engineer. */
"Do not show prompt when USB device is plugged in" = "插入 USB 裝置時不顯示提示";

/* No comment provided by engineer. */
"Do you want to copy this VM and all its data to internal storage?" = "你要複製此虛擬機及其所有資料至內置儲存裝置嗎？";

/* No comment provided by engineer. */
"Do you want to delete this VM and all its data?" = "你要刪除此虛擬機及其所有資料嗎？";

/* No comment provided by engineer. */
"Do you want to download '%@'?" = "你要下載「%@」嗎？";

/* No comment provided by engineer. */
"Do you want to duplicate this VM and all its data?" = "你要製作此虛擬機及其所有資料的副本嗎？";

/* No comment provided by engineer. */
"Do you want to force stop this VM and lose all unsaved data?" = "你要強制停止此虛擬機並遺失所有未儲存的資料嗎？";

/* No comment provided by engineer. */
"Do you want to forget all clients and generate a new server identity? Any clients that previously paired with this server will be instructed to manually unpair with this server before they can connect again." = "你要忘記所有客户端並產生新的伺服器身份嗎？之前與此伺服器配對的所有客户端都將會被指示手動取消與此伺服器的配對，然後才能再次連接。";

/* No comment provided by engineer. */
"Do you want to forget the selected client(s)?" = "你要忘記已選擇的客户端嗎？";

/* No comment provided by engineer. */
"Do you want to move this VM to another location? This will copy the data to the new location, delete the data from the original location, and then create a shortcut." = "你要移動此虛擬機至其他位置嗎？這將會複製資料至新位置，刪除原始位置資料，並製作捷徑。";

/* No comment provided by engineer. */
"Do you want to remove this shortcut? The data will not be deleted." = "你要刪除此捷徑嗎？資料不會被刪除。";

/* No comment provided by engineer. */
"Download" = "下載";

/* No comment provided by engineer. */
"Download prebuilt from UTM Gallery…" = "從 UTM 虛擬機庫下載預構建⋯";

/* No comment provided by engineer. */
"Download VM" = "下載虛擬機";

/* No comment provided by engineer. */
"Drag and drop IPSW file here" = "拖放 IPSW 檔到這裡";

/* UTMScriptingConfigImpl */
"Drive description is invalid." = "磁碟描述無效。";

/* No comment provided by engineer. */
"Drives" = "磁碟";

/* VMDrivesSettingsView */
"EFI Variables" = "EFI 變數";

/* VMDisplayWindowController */
"Eject" = "退出";

/* No comment provided by engineer. */
"Emulate" = "模擬";

/* UTMQemuConstants */
"Emulated VLAN" = "模擬 VLAN";

/* No comment provided by engineer. */
"Enable Clipboard Sharing" = "啟用剪貼板分享";

/* No comment provided by engineer. */
"Enjoying the app? Consider making a donation to support development." = "享受使用此 App 嗎？請考慮捐贈以支持開發。";

/* VMDisplayWindowController */
"Error" = "錯誤";

/* No comment provided by engineer. */
"Existing" = "現有";

/* No comment provided by engineer. */
"Export QEMU Command…" = "輸出 QEMU 指令⋯";

/* Word for decompressing a compressed folder */
"Extracting…" = "正在解壓縮⋯";

/* UTMQemuVirtualMachine */
"Failed to access data from shortcut." = "無法從捷徑取用資料。";

/* UTMQemuVirtualMachine */
"Failed to access drive image path." = "無法取用磁碟映像檔路徑。";

/* UTMRemoteClient
UTMRemoteServer */
"Failed to access file." = "無法取用檔案。";

/* UTMQemuVirtualMachine */
"Failed to access shared directory." = "無法取用分享目錄。";

/* ContentView */
"Failed to attach to JitStreamer:\n%@" = "無法附加至 JitStreamer：%@";

/* UTMData */
"Failed to attach to JitStreamer." = "無法附加至 JitStreamer。";

/* UTMSpiceIO */
"Failed to change current directory." = "無法變更現時的目錄。";

/* UTMData */
"Failed to clone VM." = "無法製作虛擬機的副本。";

/* UTMRemoteSpiceVirtualMachine */
"Failed to connect to SPICE: %@" = "無法連接至 SPICE：%@";

/* UTMPipeInterface */
"Failed to create pipe for communications." = "無法為通訊製作管道。";

/* UTMData */
"Failed to decode JitStreamer response." = "無法解碼 JitStreamer 回應。";

/* UTMRemoteClient */
"Failed to determine host name." = "無法確定主機名。";

/* UTMRemoteKeyManager */
"Failed to generate a key pair." = "無法產生密鑰配對。";

/* UTMQemuVirtualMachine */
"Failed to generate TLS key for server." = "無法為伺服器產生 TLS 密鑰。";

/* UTMRemoteClient */
"Failed to get host fingerprint." = "無法取得主機指紋。";

/* VMWizardState */
"Failed to get latest macOS version from Apple." = "無法從 Apple 取得最新的 macOS 版本。";

/* UTMRemoteKeyManager */
"Failed to import generated key." = "無法輸入已產生的密鑰。";

/* UTMQemuConfigurationError */
"Failed to migrate configuration from a previous UTM version." = "無法從之前版本的 UTM 轉移設定。";

/* UTMRemoteKeyManager */
"Failed to parse generated key pair." = "無法解析產生的密鑰對。";

/* UTMData */
"Failed to parse imported VM." = "無法解析已輸入的虛擬機。";

/* UTMDownloadVMTask */
"Failed to parse the downloaded VM." = "無法解析已下載的虛擬機。";

/* UTMData */
"Failed to reconnect to the server." = "無法重新連接至伺服器。";

/* AppDelegate
VMDisplayWindowController */
"Failed to save suspend state" = "無法儲存暫停狀態。";

/* UTMQemuVirtualMachine */
"Failed to save VM snapshot. Usually this means at least one device does not support snapshots. %@" = "無法儲存虛擬機快照。通常這代表至少有一個裝置不支援快照。%@";

/* UTMSpiceIO */
"Failed to start SPICE client." = "無法啟動 SPICE 客户端。";

/* No comment provided by engineer. */
"Faster, but can only run the native CPU architecture." = "較快，但只能執行原生 CPU 架構。";

/* No comment provided by engineer. */
"Fingerprint" = "指紋";

/* Configuration boot device
UTMQemuConstants */
"Floppy" = "軟碟";

/* No comment provided by engineer. */
"Floppy Image" = "軟碟映像檔";

/* No comment provided by engineer. */
"Font Size" = "字體大小";

/* VMDisplayWindowController */
"Force kill" = "強制結束";

/* VMDisplayWindowController */
"Force kill the VM process with high risk of data corruption." = "強制結束虛擬機程序，會有高風險損毀資料。";

/* No comment provided by engineer. */
"Force Multicore" = "強制多核心";

/* VMDisplayWindowController */
"Force shut down" = "強制關機";

/* UTMQemuConstants */
"GDB Debug Stub" = "GDB 除錯空函式";

/* No comment provided by engineer. */
"Generic" = "一般";

/* UTMAppleConfigurationDevices */
"Generic Mouse" = "一般滑鼠";

/* UTMAppleConfigurationDevices */
"Generic USB" = "一般 USB";

/* No comment provided by engineer. */
"Gesture and Cursor Settings" = "手勢與指標設定";

/* No comment provided by engineer. */
"GiB" = "GiB";

/* No comment provided by engineer. */
"Guest drivers are required for 3D acceleration." = "需要客户端驅動程式才能使用 3D 加速。";

/* Configuration boot device */
"Hard Disk" = "硬碟";

/* No comment provided by engineer. */
"Hardware" = "硬件";

/* No comment provided by engineer. */
"Hello" = "你好";

/* No comment provided by engineer. */
"Hide Unused…" = "隱藏未使用的⋯";

/* No comment provided by engineer. */
"Hold Control (⌃) for right click" = "按住 Control (⌃) 以右鍵點按";

/* No comment provided by engineer. */
"Host" = "主機";

/* UTMQemuConstants */
"Host Only" = "僅主機";

/* No comment provided by engineer. */
"Hostname or IP address" = "主機或 IP 位址";

/* No comment provided by engineer. */
"Icon" = "圖示";

/* UTMQemuConstants */
"IDE" = "IDE";

/* UTMScriptingConfigImpl */
"Identifier '%@' cannot be found." = "識別碼「%@」未找到。";

/* No comment provided by engineer. */
"Image File Type" = "映像檔種類";

/* No comment provided by engineer. */
"Import Drive…" = "輸入磁碟⋯";

/* No comment provided by engineer. */
"Import IPSW" = "輸入 IPSW";

/* No comment provided by engineer. */
"Import…" = "輸入⋯";

/* VMDetailsView */
"Inactive" = "未啟用";

/* UTMScriptingConfigImpl */
"Index %lld cannot be found." = "找不到索引「%lld」。";

/* No comment provided by engineer. */
"Information" = "資料";

/* VMDisplayAppleWindowController */
"Install Guest Tools…" = "安裝客户端工具⋯";

/* VMDisplayWindowController */
"Install Windows Guest Tools…" = "安裝 Windows 客户端工具⋯";

/* VMDisplayAppleWindowController */
"Installation: %@" = "安裝：%@";

/* UTMProcess */
"Internal error has occurred." = "發生內部錯誤。";

/* UTMSpiceIO */
"Internal error trying to connect to SPICE server." = "連接 SPICE 伺服器時發生內部錯誤。";

/* VMDisplayMetalWindowController */
"Internal error." = "內部錯誤。";

/* UTMRemoteServer */
"Invalid backend." = "無效的後端。";

/* VMWizardState */
"Invalid drive size specified." = "指定的磁碟大小無效。";

/* UTMData */
"Invalid JitStreamer attach URL:\n%@" = "無效的 JitStreamer 附加 URL：%@";

/* VMConfigAppleNetworkingView */
"Invalid MAC address." = "無效的 MAC 位址。";

/* VMWizardState */
"Invalid RAM size specified." = "指定的記憶體大小無效。";

/* No comment provided by engineer. */
"Invert scrolling" = "反轉捲動";

/* No comment provided by engineer. */
"IP Configuration" = "IP 設定";

/* No comment provided by engineer. */
"Isolate Guest from Host" = "從主機隔離客户端";

/* UTMQemuConstants */
"Italic" = "斜體";

/* UTMQemuConstants */
"Italic, Bold" = "斜體，粗體";

/* No comment provided by engineer. */
"Keep UTM running after last window is closed and all VMs are shut down" = "在最後一個視窗關閉並且所有虛擬機關機時繼續執行 UTM";

/* No comment provided by engineer. */
"License" = "許可協議";

/* UTMQemuConstants */
"Linear" = "線性";

/* UTMAppleConfigurationBoot */
"Linux" = "Linux";

/* UTMLegacyQemuConfiguration
UTMQemuConstants */
"Linux Device Tree Binary" = "Linux 裝置樹二進位檔";

/* No comment provided by engineer. */
"Linux initial ramdisk (optional)" = "Linux 初始 RAM 磁碟 (可留空)";

/* UTMLegacyQemuConfiguration
UTMQemuConstants */
"Linux Kernel" = "Linux 核心";

/* No comment provided by engineer. */
"Linux kernel (required)" = "Linux 核心 (必填)";

/* UTMLegacyQemuConfiguration
UTMQemuConstants */
"Linux RAM Disk" = "Linux RAM 磁碟";

/* No comment provided by engineer. */
"Linux Root FS Image (optional)" = "Linux rootfs 映像檔 (可留空)";

/* No comment provided by engineer. */
"Linux Settings" = "Linux 設定";

/* No comment provided by engineer. */
"Logging" = "記錄";

/* UTMAppleConfigurationDevices */
"Mac Keyboard (macOS 14+)" = "Mac 鍵盤 (macOS 14+)";

/* UTMAppleConfigurationDevices */
"Mac Trackpad (macOS 13+)" = "Mac 觸控板 (macOS 13+)";

/* UTMAppleConfigurationBoot */
"macOS" = "macOS";

/* UTMDownloadMacSupportToolsTask */
"macOS Guest Support Tools" = "macOS 客户端支援工具";

/* VMWizardOSMacView */
"macOS guests are only supported on ARM64 devices." = "macOS 客户端只支援 ARM64 裝置。";

/* VMWizardState */
"macOS is not supported with QEMU." = "QEMU 不支援 macOS。";

/* No comment provided by engineer. */
"macOS Settings" = "macOS 設定";

/* No comment provided by engineer. */
"Make sure the latest version of UTM is running on your Mac and UTM Server is enabled. You can download UTM from the Mac App Store." = "請確定最新版本的 UTM 在你的 Mac 上執行，並且已啟用 UTM 伺服器。你可以從 Mac App Store 下載 UTM。";

/* UTMQemuConstants */
"Manual Serial Device (advanced)" = "手動序列裝置 (進階)";

/* No comment provided by engineer. */
"Maximum Shared USB Devices" = "最多分享 USB 裝置";

/* No comment provided by engineer. */
"Memory" = "記憶體";

/* VMDisplayMetalWindowController */
"Metal is not supported on this device. Cannot render display." = "此裝置不支援 Metal，無法運算顯示。";

/* No comment provided by engineer. */
"MiB" = "MiB";

/* No comment provided by engineer. */
"Minimum size: %@" = "最低大小：%@";

/* UTMDonateView */
"month" = "月";

/* No comment provided by engineer. */
"Mouse/Keyboard" = "滑鼠/鍵盤";

/* No comment provided by engineer. */
"Move Down" = "向下移動";

/* No comment provided by engineer. */
"Move Up" = "向上移動";

/* UTMQemuConstants */
"MTD (NAND/NOR)" = "MTD (NAND/NOR)";

/* No comment provided by engineer. */
"Name" = "名稱";

/* No comment provided by engineer. */
"Name (optional)" = "名稱 (可留空)";

/* UTMQemuConstants */
"Nearest Neighbor" = "近鄰取樣";

/* No comment provided by engineer. */
"Network" = "網絡";

/* No comment provided by engineer. */
"New" = "新增";

/* No comment provided by engineer. */
"New Drive…" = "新增磁碟⋯";

/* No comment provided by engineer. */
"New Machine" = "新增虛擬機";

/* No comment provided by engineer. */
"New…" = "新增⋯";

/* No comment provided by engineer. */
"No" = "否";

/* UTMScriptingAppDelegate */
"No architecture specified in the configuration." = "設定中未指定架構。";

/* VMDisplayWindowController */
"No drives connected." = "沒有已連接的磁碟。";

/* UTMDownloadSupportToolsTaskError */
"No empty removable drive found. Make sure you have at least one removable drive that is not in use." = "無法找到可移除的空白磁碟。請確定你至少有一個未使用的可移除的磁碟。";

/* UTMScriptingAppDelegate */
"No file specified in the command." = "命令中未指定檔案。";

/* UTMScriptingAppDelegate */
"No name specified in the configuration." = "設定中未指定名稱。";

/* No comment provided by engineer. */
"No output device is selected for this window." = "此視窗未選擇輸出裝置。";

/* No comment provided by engineer. */
"No release notes found for version %@." = "無法找到版本 %@ 的版本備註。";

/* VMQemuDisplayMetalWindowController */
"No USB devices detected." = "未偵測到 USB 裝置。";

/* No comment provided by engineer. */
"No virtual machines found." = "未找到虛擬機。";

/* VMDisplayAppleDisplayController
VMDisplayQemuDisplayController
VMToolbarDriveMenuView */
"none" = "無";

/* UTMAppleConfigurationBoot
UTMLegacyQemuConfiguration
UTMQemuConstants */
"None" = "無";

/* UTMQemuConstants */
"None (Advanced)" = "無 (進階)";

/* UTMRemoteServer */
"Not authenticated." = "未認證。";

/* UTMVirtualMachine */
"Not implemented." = "尚未執行。";

/* No comment provided by engineer. */
"Notes" = "備註";

/* No comment provided by engineer. */
"Num Lock is forced on" = "Num Lock 強制開啟";

/* UTMQemuConstants */
"NVMe" = "NVMe";

/* VMDisplayWindowController */
"OK" = "好";

/* UTMScriptingVirtualMachineImpl */
"One or more required parameters are missing or invalid." = "一個或多個所需的引數缺失或無效。";

/* No comment provided by engineer. */
"Open…" = "開啟⋯";

/* No comment provided by engineer. */
"Operating System" = "作業系統";

/* UTMScriptingVirtualMachineImpl */
"Operation not available." = "操作無法使用。";

/* UTMData
UTMScriptingVirtualMachineImpl */
"Operation not supported by the backend." = "後端不支援此操作。";

/* No comment provided by engineer. */
"Option (⌥) is Meta key" = "將 Option (⌥) 作為 Meta 鍵";

/* No comment provided by engineer. */
"Options" = "選項";

/* No comment provided by engineer. */
"Other" = "其他";

/* No comment provided by engineer. */
"Password" = "密碼";

/* UTMRemoteClient */
"Password is incorrect." = "密碼不正確。";

/* UTMRemoteClient */
"Password is required." = "需要密碼。";

/* VMDisplayWindowController */
"Pause" = "暫停";

/* VMData */
"Paused" = "已暫停";

/* VMData */
"Pausing" = "正在暫停";

/* UTMQemuConstants */
"PC System Flash" = "PC 系統快閃記憶體";

/* No comment provided by engineer. */
"Pending" = "待處理";

/* UTMDonateView */
"period" = "時段";

/* VMDisplayWindowController */
"Play" = "播放";

/* VMWizardState */
"Please select a boot image." = "請選擇一個啟動映像檔。";

/* VMWizardState */
"Please select a kernel file." = "請選擇一個核心檔。";

/* No comment provided by engineer. */
"Please select a macOS recovery IPSW." = "請選擇一個 IPSW 還原檔。";

/* No comment provided by engineer. */
"Please select an uncompressed Linux kernel image." = "請選擇一個未壓縮的 Linux 核心映像檔。";

/* No comment provided by engineer. */
"Port" = "埠";

/* No comment provided by engineer. */
"Port Forward" = "連接埠轉寄";

/* No comment provided by engineer. */
"Preconfigured" = "預設定";

/* A download process is about to begin. */
"Preparing…" = "正在準備⋯";

/* VMDisplayQemuMetalWindowController */
"Press %@ to release cursor" = "按一下 %@ 以放開指標";

/* No comment provided by engineer. */
"Prevent system from sleeping when any VM is running" = "執行任何虛擬機時避免系統睡眠";

/* UTMAppleConfigurationTerminal
UTMQemuConstants */
"Pseudo-TTY Device" = "Pseudo-TTY 裝置";

/* No comment provided by engineer. */
"QEMU Arguments" = "QEMU 引數";

/* No comment provided by engineer. */
"QEMU Graphics Acceleration" = "QEMU 圖形加速";

/* No comment provided by engineer. */
"QEMU Keyboard" = "QEMU 鍵盤";

/* UTMQemuConstants */
"QEMU Monitor (HMP)" = "QEMU 顯示器 (HMP)";

/* No comment provided by engineer. */
"QEMU Pointer" = "QEMU 指標";

/* No comment provided by engineer. */
"QEMU Sound" = "QEMU 聲音";

/* No comment provided by engineer. */
"QEMU USB" = "QEMU USB";

/* VMDisplayWindowController */
"Querying drives status..." = "正在查詢磁碟狀態⋯";

/* VMQemuDisplayMetalWindowController */
"Querying USB devices..." = "正在查詢 USB 裝置⋯";

/* VMQemuDisplayMetalWindowController */
"Quitting UTM will kill all running VMs." = "結束 UTM 會結束所有執行的虛擬機。";

/* No comment provided by engineer. */
"Raw Image" = "raw 映像檔";

/* VMDisplayAppleController */
"Read Only" = "唯讀";

/* No comment provided by engineer. */
"Reclaim" = "回收空間";

/* UTMQemuConstants */
"Regular" = "一般";

/* VMRemovableDrivesView */
"Removable" = "可移除";

/* No comment provided by engineer. */
"Removable Drive" = "可移除的磁碟";

/* No comment provided by engineer. */
"Remove" = "刪除";

/* VMDisplayAppleController */
"Remove…" = "刪除⋯";

/* VMDisplayWindowController */
"Request power down" = "要求關閉電源";

/* No comment provided by engineer. */
"Reset" = "重設";

/* No comment provided by engineer. */
"Reset Identity" = "重設身份";

/* No comment provided by engineer. */
"Resize" = "調整大小";

/* No comment provided by engineer. */
"Resize display to screen size and orientation automatically" = "自動將顯示調整為螢幕大小與方向";

/* No comment provided by engineer. */
"Resize display to window size automatically" = "自動將顯示調整為視窗大小";

/* No comment provided by engineer. */
"Resizing is experimental and could result in data loss. You are strongly encouraged to back-up this VM before proceeding. Would you like to resize to %@ GiB?" = "調整空間大小是試驗性功能，可能會引致資料遺失。進行前，強烈建議你備份此虛擬機。你想調整大小為 %@ GiB 嗎？";

/* VMData */
"Restoring" = "正在還原";

/* VMData */
"Resuming" = "正在繼續";

/* No comment provided by engineer. */
"Retina Mode" = "Retina 模式";

/* UTMAppleConfiguration */
"Rosetta is not supported on the current host machine." = "目前的主機不支援 Rosetta。";

/* No comment provided by engineer. */
"Running" = "正在執行";

/* No comment provided by engineer. */
"Running low on memory! UTM might soon be killed by iOS. You can prevent this by decreasing the amount of memory and/or JIT cache assigned to this VM" = "記憶體不足！UTM 可能即將被 iOS 結束。你可以透過減少指定至此虛擬機的記憶體和/或 JIT 快取以避免這種問題。";

/* No comment provided by engineer. */
"Save" = "儲存";

/* No comment provided by engineer. */
"Saved" = "已儲存";

/* VMData */
"Saving" = "正在儲存";

/* No comment provided by engineer. */
"Scaling" = "縮放";

/* UTMQemuConstants */
"SCSI" = "SCSI";

/* UTMQemuConstants */
"SD Card" = "SD 卡";

/* No comment provided by engineer. */
"Select a file." = "選取檔案。";

/* No comment provided by engineer. */
"Select a UTM Server" = "選取 UTM 伺服器";

/* VMDisplayWindowController */
"Select Drive Image" = "選取磁碟映像檔";

/* VMDisplayAppleWindowController
VMDisplayWindowController */
"Select Shared Folder" = "選取分享的資料夾";

/* SavePanel */
"Select where to export QEMU command:" = "選取輸出 QEMU 指令的位置：";

/* SavePanel */
"Select where to save debug log:" = "選取儲存除錯記錄的位置：";

/* SavePanel */
"Select where to save UTM Virtual Machine:" = "選取儲存 UTM 虛擬機的位置：";

/* No comment provided by engineer. */
"Selected:" = "已選取：";

/* VMDisplayWindowController */
"Sends power down request to the guest. This simulates pressing the power button on a PC." = "傳送關閉電源要求至客户端。此操作模擬了按一下 PC 上的電源按鈕。";

/* VMDisplayAppleWindowController
VMDisplayQemuDisplayController */
"Serial %lld" = "序列裝置 %lld";

/* Server view */
"Server" = "伺服器";

/* No comment provided by engineer. */
"Server IP: %@, Port: %@" = "伺服器 IP 位址：%1$@，埠：%2$@";

/* No comment provided by engineer. */
"Share USB devices from host" = "從主機分享 USB 裝置";

/* No comment provided by engineer. */
"Shared directories in macOS VMs are only available in macOS 13 and later." = "macOS 虛擬機分享目錄只可以在 macOS 13 和較新版本上使用。";

/* No comment provided by engineer. */
"Shared Directory" = "已分享目錄";

/* UTMAppleConfigurationNetwork
UTMQemuConstants */
"Shared Network" = "已分享網絡";

/* No comment provided by engineer. */
"Sharing" = "分享";

/* No comment provided by engineer. */
"Show Advanced Settings" = "顯示進階設定";

/* No comment provided by engineer. */
"Show All" = "顯示全部";

/* No comment provided by engineer. */
"Show All…" = "顯示全部⋯";

/* No comment provided by engineer. */
"Show dock icon" = "顯示 Dock 圖示";

/* No comment provided by engineer. */
"Show menu bar icon" = "顯示選單列圖示";

/* No comment provided by engineer. */
"Size" = "大小";

/* No comment provided by engineer. */
"Slower, but can run other CPU architectures." = "較慢，但可以執行其他 CPU 架構。";

/* UTMSWTPM */
"Socket not specified." = "未指定 Socket。";

/* No comment provided by engineer. */
"Specify the size of the drive where data will be stored into." = "指定儲存資料的磁碟大小。";

/* UTMQemuConstants */
"SPICE WebDAV" = "SPICE WebDAV";

/* No comment provided by engineer. */
"SPICE with GStreamer (Input & Output)" = "SPICE with GStreamer (輸入與輸出)";

/* No comment provided by engineer. */
"Start Here" = "從這裡開始";

/* VMData */
"Started" = "已啟動";

/* VMData */
"Starting" = "正在啟動";

/* No comment provided by engineer. */
"Startup" = "啟動";

/* No comment provided by engineer. */
"Stop" = "停止";

/* VMData */
"Stopped" = "已停止";

/* VMData */
"Stopping" = "正在停止";

/* No comment provided by engineer. */
"Style" = "樣式";

/* No comment provided by engineer. */
"Summary" = "摘要";

/* Welcome view */
"Support" = "支援";

/* No comment provided by engineer. */
"Support UTM" = "支持 UTM";

/* UTMQemuVirtualMachine */
"Suspend is not supported for virtualization." = "暫停功能不支援虛擬化。";

/* UTMQemuVirtualMachine */
"Suspend is not supported when an emulated NVMe device is active." = "模擬 NVMe 裝置啟動時不支援暫停功能。";

/* UTMQemuVirtualMachine */
"Suspend is not supported when GPU acceleration is enabled." = "GPU 加速啟用時不支援暫停功能。";

/* UTMQemuVirtualMachine */
"Suspend state cannot be saved when running in disposible mode." = "「即棄式模式」執行時無法儲存暫停狀態。";

/* VMData */
"Suspended" = "暫停";

/* UTMSWTPM */
"SW TPM failed to start. %@" = "SW TPM 無法啟動。%@";

/* VMSessionState */
"Switch back to UTM to avoid termination." = "切換回至 UTM 以避免終止。";

/* No comment provided by engineer. */
"System" = "系統";

/* No comment provided by engineer. */
"Tap to hide/show toolbar" = "點一下以隱藏/顯示工具列";

/* UTMQemuConstants */
"TCP" = "TCP";

/* UTMQemuConstants */
"TCP Client Connection" = "TCP 客户端連線";

/* UTMQemuConstants */
"TCP Server Connection" = "TCP 伺服器連線";

/* VMDisplayWindowController */
"Tells the VM process to shut down with risk of data corruption. This simulates holding down the power button on a PC." = "讓虛擬機程序關機，會有風險損毀資料。此操作模擬了在 PC 上按一下電源按鈕。";

/* No comment provided by engineer. */
"Test" = "測試";

/* No comment provided by engineer. */
"Test 1" = "測試 1";

/* No comment provided by engineer. */
"Test 2" = "測試 2";

/* UTMConfiguration */
"The backend for this configuration is not supported." = "不支援此設定的後端。";

/* UTMRemoteServer */
"The client interface version does not match the server." = "客户端介面版本與伺服器不相符。";

/* UTMScriptingUSBDeviceImpl */
"The device cannot be found." = "無法找到此裝置。";

/* UTMScriptingUSBDeviceImpl */
"The device is not currently connected." = "此裝置現時尚未連接。";

/* UTMConfiguration */
"The drive '%@' already exists and cannot be created." = "磁碟「%@」已存在，無法製作。";

/* UTMDownloadSupportToolsTaskError */
"The guest support tools have already been mounted." = "此客户端支援工具已經被裝載了。";

/* UTMRemoteClient */
"The host fingerprint does not match the saved value. This means that UTM Server was reset, a different host is using the same name, or an attacker is pretending to be the host. For your protection, you need to delete this saved host to continue." = "主機指紋與儲存的值不相符。這代表 UTM 伺服器已重設，或是不同的主機使用相同的名稱，或是攻擊者正在偽裝成主機。為了保障你的資料，你需要刪除此儲存的主機以繼續。";

/* UTMAppleConfiguration */
"The host operating system needs to be updated to support one or more features requested by the guest." = "需要更新主機作業系統以支援客户端要求的一個或多個功能。";

/* UTMAppleVirtualMachine */
"The operating system cannot be installed on this machine." = "無法在此電腦上安裝作業系統。";

/* UTMAppleVirtualMachine */
"The operation is not available." = "此操作無法使用。";

/* UTMScriptingVirtualMachineImpl */
"The QEMU guest agent is not running or not installed on the guest." = "QEMU 客户端代理程式未執行，或是未在客户端上安裝。";

/* No comment provided by engineer. */
"The selected architecture is unsupported in this version of UTM." = "此版本的 UTM 不支援所選的架構。";

/* VMWizardState */
"The selected boot image contains the word '%@' but the guest architecture is '%@'. Please ensure you have selected an image that is compatible with '%@'." = "選擇的啟動映像檔包含單字「%1$@」，但客户端架構為「%2$@」。請確定你選擇了與「%3$@」相容的映像檔。";

/* UTMRemoteClient */
"The server interface version does not match the client." = "伺服器介面版本與客户端不相符。";

/* No comment provided by engineer. */
"The target does not support hardware emulated serial connections." = "目標平台不支援硬件模擬序列連線。";

/* UTMQemuVirtualMachine */
"The virtual machine is in an invalid state." = "虛擬機的狀態無效。";

/* UTMScriptingVirtualMachineImpl */
"The virtual machine is not running." = "虛擬機未執行。";

/* UTMScriptingVirtualMachineImpl */
"The virtual machine must be stopped before this operation can be performed." = "必須停止虛擬機才能執行此操作。";

/* Error shown when importing a ZIP file from web that doesn't contain a UTM Virtual Machine. */
"There is no UTM file in the downloaded ZIP archive." = "已下載的 ZIP 封存檔中沒有 UTM 檔案。";

/* No comment provided by engineer. */
"This audio card is not supported." = "不支援此聲卡。";

/* UTMScriptingAppDelegate */
"This backend is not supported on your machine." = "你的電腦不支援此後端。";

/* No comment provided by engineer. */
"This build does not emulation." = "此構建不支援模擬。";

/* UTMQemuVirtualMachine */
"This build of UTM does not support emulating the architecture of this VM." = "此 UTM 構建不支援模擬此虛擬機的架構。";

/* VMConfigSystemView */
"This change will reset all settings" = "此變更會重設全部設定";

/* UTMConfiguration */
"This configuration is saved with a newer version of UTM and is not compatible with this version." = "此設定使用較新版本的 UTM 儲存，與此版本不相容。";

/* UTMConfiguration */
"This configuration is too old and is not supported." = "此設定太舊，且不支援。";

/* UTMScriptingConfigImpl */
"This device is not supported by the target." = "目標不支援此裝置。";

/* VMConfigAppleSharingView */
"This directory is already being shared." = "此目錄已被分享。";

/* VMData */
"This function is not implemented." = "此功能尚未能執行。";

/* UTMData */
"This functionality is not yet implemented." = "此功能尚未能執行。";

/* UTMRemoteClient */
"This host is not yet trusted. You should verify that the fingerprints match what is displayed on the host and then select Trust to continue." = "此主機尚未受信任。你應該驗證指紋是否與主機上顯示的相符，然後選擇「信任」以繼續。";

/* UTMAppleConfiguration */
"This is not a valid Apple Virtualization configuration." = "不是有效的 Apple 虛擬化設定。";

/* VMDisplayWindowController */
"This may corrupt the VM and any unsaved changes will be lost. To quit safely, shut down from the guest." = "這可能會損毀虛擬機，所有未儲存的變更都將遺失。如要安全退出，請從客户端關機。";

/* No comment provided by engineer. */
"This operating system is unsupported on your machine." = "你的電腦不支援此作業系統。";

/* UTMDataExtension */
"This virtual machine cannot be run on this machine." = "此虛擬機無法在電腦上執行。";

/* UTMAppleConfiguration */
"This virtual machine cannot run on the current host machine." = "此虛擬機無法在現有的主機上執行。";

/* UTMAppleConfiguration */
"This virtual machine contains an invalid hardware model. The configuration may be corrupted or is outdated." = "此虛擬機含有無效的硬件型號。其設定可能損毀或過時。";

/* No comment provided by engineer. */
"This virtual machine has been removed." = "此虛擬機已移除。";

/* UTMDataExtension */
"This virtual machine is already running. In order to run it from this device, you must stop it first." = "此虛擬機已在執行。為了從此裝置執行，你必須先停止它。";

/* UTMData */
"This virtual machine is currently unavailable, make sure it is not open in another session." = "現時無法使用此虛擬機，請確定它沒有在其他會話中開啟。";

/* VMData */
"This VM is not available or is configured for a backend that does not support remote clients." = "此虛擬機無法使用，或設定為不支援遙距客户端的後端。";

/* No comment provided by engineer. */
"This VM is unavailable." = "此虛擬機無法使用。";

/* VMDisplayWindowController */
"This will reset the VM and any unsaved state will be lost." = "這會重設虛擬機，任何未儲存的狀態都將遺失。";

/* UTMRemoteConnectView */
"Timed out trying to connect." = "嘗試連接超時。";

/* VMDisplayAppleWindowController */
"To access the shared directory, the guest OS must have Virtiofs drivers installed. You can then run `sudo mount -t virtiofs share /path/to/share` to mount to the share path." = "如要取用分享目錄，客户端作業系統必須安裝 VirtioFS 驅動程式。然後你可以執行「sudo mount -t virtiofs share /path/to/share」以裝載到分享路徑。";

/* VMMetalView */
"To capture input or to release the capture, press Command and Option at the same time." = "如要擷取或放開輸入，請同時按一下 Command + Option。";

/* No comment provided by engineer. */
"To install macOS, you need to download a recovery IPSW. If you do not select an existing IPSW, the latest macOS IPSW will be downloaded from Apple." = "如要安裝 macOS，你需要下載 IPSW 還原檔。如你未選擇現有的 IPSW，將會從 Apple 下載最新的 macOS IPSW。";

/* VMDisplayQemuMetalWindowController */
"To release the mouse cursor, press %@ at the same time." = "如要放開滑鼠指標，請同時按一下 %@。";

/* No comment provided by engineer. */
"Trust" = "信任";

/* No comment provided by engineer. */
"u{2022} " = "u{2022}";

/* UTMQemuConstants */
"UDP" = "UDP";

/* No comment provided by engineer. */
"UEFI" = "UEFI";

/* UTMQemuConfigurationError */
"UEFI is not supported with this architecture." = "此架構不支援 UEFI。";

/* UTMData */
"Unable to add a shortcut to the new location." = "無法將捷徑加至新位置。";

/* VMData */
"Unavailable" = "無法使用";

/* VMWizardState */
"Unavailable for this platform." = "在此平台上無法使用。";

/* No comment provided by engineer. */
"Uncompressed Linux initial ramdisk (optional)" = "未壓縮的 Linux 初始 RAM 磁碟 (可留空)";

/* No comment provided by engineer. */
"Uncompressed Linux kernel (required)" = "未壓縮的 Linux 核心映像檔 (必填)";

/* No comment provided by engineer. */
"Update Interface" = "更新介面";

/* UTMQemuConstants */
"USB" = "USB";

/* UTMQemuConstants */
"USB 2.0" = "USB 2.0";

/* UTMQemuConstants */
"USB 3.0 (XHCI)" = "USB 3.0 (XHCI)";

/* VMQemuDisplayMetalWindowController */
"USB Device" = "USB 裝置";

/* VMDisplayAppleDisplayController */
"USB Mass Storage: %@" = "USB 大容量儲存裝置：%@";

/* No comment provided by engineer. */
"USB Sharing" = "USB 分享";

/* No comment provided by engineer. */
"USB sharing not supported in this build of UTM." = "此 UTM 構建不支援 USB 分享。";

/* No comment provided by engineer. */
"Use Command+Option (⌘+⌥) for input capture/release" = "使用 Command + Option (⌘ + ⌥) 以擷取/放開輸入";

/* No comment provided by engineer. */
"Use NVMe Interface" = "使用 NVMe 介面";

/* Welcome view */
"User Guide" = "用户指南";

/* UTMScriptingAppDelegate
UTMScriptingUSBDeviceImpl */
"UTM is not ready to accept commands." = "UTM 尚未準備好接受指令。";

/* No comment provided by engineer. */
"Version" = "版本";

/* UTMQemuConstants */
"VirtFS" = "VirtFS";

/* UTMQemuConstants */
"VirtIO" = "VirtIO";

/* UTMConfigurationInfo
UTMData
VMMetalView */
"Virtual Machine" = "虛擬機";

/* No comment provided by engineer. */
"Virtual Machine Gallery" = "虛擬機庫";

/* VMData */
"Virtual machine not loaded." = "未載入虛擬機。";

/* No comment provided by engineer. */
"Virtualization is not supported on your system." = "你的電腦系統不支援虛擬化。";

/* No comment provided by engineer. */
"Virtualize" = "虛擬化";

/* No comment provided by engineer. */
"Waiting for VM to connect to display..." = "正在等待虛擬機連接至顯示⋯";

/* UTMDonateView */
"week" = "周";

/* No comment provided by engineer. */
"Welcome to UTM" = "歡迎使用 UTM";

/* No comment provided by engineer. */
"What's New" = "新功能";

/* No comment provided by engineer. */
"When the toolbar is hidden, the icon will disappear after a few seconds. To show the icon again, tap anywhere on the screen." = "當工具列隱藏時，圖示將會在幾秒鐘之後消失。如要再次顯示圖示，請點一下螢幕上的任意位置。";

/* UTMDownloadSupportToolsTask */
"Windows Guest Support Tools" = "Windows 客户端支援工具";

/* VMQemuDisplayMetalWindowController */
"Would you like to connect '%@' to this virtual machine?" = "你想連接「%@」至此虛擬機嗎？";

/* VMDisplayAppleWindowController */
"Would you like to install macOS? If an existing operating system is already installed on the primary drive of this VM, then it will be erased." = "你想安裝 macOS 嗎？如此虛擬機的主磁碟已安裝現有的作業系統，則其會被清除。";

/* No comment provided by engineer. */
"Would you like to re-convert this disk image to reclaim unused space and apply compression? Note this will require enough temporary space to perform the conversion. Compression only applies to existing data and new data will still be written uncompressed. You are strongly encouraged to back-up this VM before proceeding." = "你想重新轉換此磁碟映像檔以回收未使用的空間並套用壓縮嗎？請注意，這將需要足夠的臨時空間以執行轉換，壓縮只套用至現有的資料，新資料仍將以未壓縮寫入。進行前，強烈建議你備份此虛擬機。";

/* No comment provided by engineer. */
"Would you like to re-convert this disk image to reclaim unused space? Note this will require enough temporary space to perform the conversion. You are strongly encouraged to back-up this VM before proceeding." = "你想重新轉換此磁碟映像檔以回收未使用的空間嗎？請注意，這將需要足夠的臨時空間以執行轉換。進行前，強烈建議你備份此虛擬機。";

/* UTMDonateView */
"year" = "年";

/* No comment provided by engineer. */
"Yes" = "是";

/* VMConfigSystemView */
"Your device has %llu MB of memory and the estimated usage is %llu MB." = "你的裝置有 %1$llu MB 的記憶體，預計使用量為 %2$llu MB。";

/* VMConfigAppleBootView
VMWizardOSMacView */
"Your machine does not support running this IPSW." = "你的電腦不支援執行此 IPSW。";

/* UTMDonateView */
"Your purchase could not be verified by the App Store." = "App Store 無法驗證你的購買。";

/* No comment provided by engineer. */
"Your support is the driving force that helps UTM stay independent. Your contribution, no matter the size, makes a significant difference. It enables us to develop new features and maintain existing ones. Thank you for considering a donation to support us." = "你的支持是幫助 UTM 保持獨立的動力。無論你的貢獻多少，都會帶來重大影響。這可以讓我們開發新功能，並維護現有的功能。多謝你考慮捐贈以支持我們。";

/* ContentView */
"Your version of iOS does not support running VMs while unmodified. You must either run UTM while jailbroken or with a remote debugger attached. See https://getutm.app/install/ for more details." = "你的 iOS 版本不支援未作變更時執行虛擬機，必須在越獄 (jailbreak) 時執行 UTM，或在附加遠程除錯器時執行 UTM。有關更多詳細資料，請瀏覽 https://getutm.app/install/。";

// Additional Strings (These strings are unable to be extracted by Xcode)

/* No comment provided by engineer. */
"" = "";

/* No comment provided by engineer. */
"(Delete)" = "(刪除)";

/* No comment provided by engineer. */
"Add" = "加入";

/* No comment provided by engineer. */
"Add a new device." = "加入一個新裝置。";

/* No comment provided by engineer. */
"Add a new drive." = "加入一個新磁碟。";

/* No comment provided by engineer. */
"Add read only" = "加入唯讀";

/* No comment provided by engineer. */
"Advanced" = "進階";

/* No comment provided by engineer. */
"Advanced. If checked, a raw disk image is used. Raw disk image does not support snapshots and will not dynamically expand in size." = "進階選項。如選擇，將會使用 raw 磁碟映像。raw 磁碟映像不支援快照，亦不會動態擴充套件大小。";

/* No comment provided by engineer. */
"Allow access from external clients" = "允許外部客户端訪問";

/* No comment provided by engineer. */
"Allow Remote Connection" = "允許遙距連線";

/* No comment provided by engineer. */
"Allows passing through additional input from trackpads. Only supported on macOS 13+ guests." = "允許透過觸控板額外輸入。只支援 macOS 13+ 客户端。";

/* No comment provided by engineer. */
"Any" = "任意";

/* No comment provided by engineer. */
"Apple Virtualization is experimental and only for advanced use cases. Leave unchecked to use QEMU, which is recommended." = "Apple 虛擬化為試驗性功能，只用作進階用例。如要使用 QEMU，建議取消剔選。";

/* No comment provided by engineer. */
"Application" = "應用程式";

/* No comment provided by engineer. */
"Architecture" = "架構";

/* No comment provided by engineer. */
"Arguments" = "引數";

/* No comment provided by engineer. */
"Auto Resolution" = "自動調整解像度";

/* No comment provided by engineer. */
"Automatic" = "自動";

/* No comment provided by engineer. */
"Automatically start UTM server" = "自動啟動 UTM 伺服器";

/* No comment provided by engineer. */
"Background Color" = "背景顏色";

/* No comment provided by engineer. */
"Balloon Device" = "Balloon 裝置";

/* No comment provided by engineer. */
"Blinking cursor?" = "閃爍指標？";

/* No comment provided by engineer. */
"Boot arguments" = "啟動引數";

/* No comment provided by engineer. */
"Boot Arguments" = "啟動引數";

/* No comment provided by engineer. */
"Boot Device" = "啟動裝置";

/* No comment provided by engineer. */
"Boot from kernel image" = "從核心映像檔啟動";

/* No comment provided by engineer. */
"Boot Image" = "啟動映像檔";

/* No comment provided by engineer. */
"Boot Image Type" = "啟動映像檔種類";

/* No comment provided by engineer. */
"Boot into recovery mode." = "啟動至「還原模式」。";

/* No comment provided by engineer. */
"Bootloader" = "Bootloader";

/* No comment provided by engineer. */
"Bridged Interface" = "橋連介面";

/* No comment provided by engineer. */
"Bridged Settings" = "橋連設定";

/* No comment provided by engineer. */
"By default, the best backend for the target will be used. If the selected backend is not available for any reason, an alternative will automatically be selected." = "根據預設，將會使用目標的最佳後端。如選擇的後端因故無法使用，將會自動選擇替用後端。";

/* No comment provided by engineer. */
"By default, the best renderer for this device will be used. You can override this with to always use a specific renderer. This only applies to QEMU VMs with GPU accelerated graphics." = "根據預設，將會使用最適合此裝置的算圖器。你可以覆蓋它以始終使用特定的算圖器，此設定只套用至支援 GPU 加速圖形的 QEMU 虛擬機。";

/* No comment provided by engineer. */
"Calculating current size..." = "計算現時大小⋯";

/* No comment provided by engineer. */
"Cancel Download" = "取消下載";

/* No comment provided by engineer. */
"Change…" = "變更⋯";

/* No comment provided by engineer. */
"Clear…" = "清除⋯";

/* No comment provided by engineer. */
"Clipboard Sharing" = "剪貼板分享";

/* No comment provided by engineer. */
"Clone" = "製作副本";

/* No comment provided by engineer. */
"Clone selected VM" = "製作已選擇的虛擬機的副本";

/* No comment provided by engineer. */
"Clone…" = "製作副本⋯";

/* No comment provided by engineer. */
"Close" = "關閉";

/* No comment provided by engineer. */
"Closing a VM without properly shutting it down could result in data loss." = "在未正確關機的情況下關閉虛擬機可能會引致資料遺失。";

/* No comment provided by engineer. */
"Compress" = "壓縮";

/* No comment provided by engineer. */
"Compress by re-converting the disk image and compressing the data." = "透過重新轉換磁碟映像檔與壓縮資料以壓縮。";

/* No comment provided by engineer. */
"Create a new VM" = "製作新虛擬機";

/* No comment provided by engineer. */
"Create a new VM with the same configuration as this one but without any data." = "製作與此相同設定的新虛擬機，但無任何資料。";

/* No comment provided by engineer. */
"Create an empty drive." = "製作空磁碟。";

/* No comment provided by engineer. */
"Debian Install Guide" = "Debian 安裝指南";

/* No comment provided by engineer. */
"Default is 1/4 of the RAM size (above). The JIT cache size is additive to the RAM size in the total memory usage!" = "預設值為記憶體大小的 1/4 (見上)。JIT 快取資料大小亦會包括於所有記憶體使用量當中！";

/* No comment provided by engineer. */
"Delete this drive." = "刪除此磁碟。";

/* No comment provided by engineer. */
"Delete selected VM" = "刪除已選擇的虛擬機";

/* No comment provided by engineer. */
"Delete this shortcut. The underlying data will not be deleted." = "刪除此捷徑。基礎資料不會被刪除。";

/* No comment provided by engineer. */
"Delete this VM and all its data." = "刪除此虛擬機及其所有資料。";

/* No comment provided by engineer. */
"Delete Drive" = "刪除磁碟";

/* No comment provided by engineer. */
"Description" = "註解";

/* No comment provided by engineer. */
"Devices" = "裝置";

/* No comment provided by engineer. */
"Directory" = "目錄";

/* No comment provided by engineer. */
"Directory Share Mode" = "目錄分享模式";

/* No comment provided by engineer. */
"Disk" = "磁碟";

/* No comment provided by engineer. */
"DHCP Domain Name" = "DHCP 網域名稱";

/* No comment provided by engineer. */
"DHCP End" = "DHCP 結束";

/* No comment provided by engineer. */
"DNS Search Domains" = "DNS 搜尋網域";

/* No comment provided by engineer. */
"DNS Server" = "DNS 伺服器";

/* No comment provided by engineer. */
"DNS Server (IPv6)" = "DNS 伺服器 (IPv6)";

/* No comment provided by engineer. */
"DHCP Start" = "DHCP 開始";

/* No comment provided by engineer. */
"Done" = "完成";

/* No comment provided by engineer. */
"Duplicate this VM along with all its data." = "製作此虛擬機及其所有資料的副本。";

/* No comment provided by engineer. */
"Download and mount the guest support package for Windows. This is required for some features including dynamic resolution and clipboard sharing." = "下載並裝載 Windows 客户端支援套件。這對於一些功能為必需，當中包括動態解像度與剪貼板分享。";

/* No comment provided by engineer. */
"Download and mount the guest tools for Windows." = "下載並裝載 Windows 客户端工具。";

/* No comment provided by engineer. */
"Download Windows 11 for ARM64 Preview VHDX" = "下載 Windows 11 ARM64 Preview VHDX 映像檔";

/* No comment provided by engineer. */
"Downscaling" = "細化解像度";

/* No comment provided by engineer. */
"Dynamic Resolution" = "動態解像度";

/* No comment provided by engineer. */
"Edit" = "編輯";

/* No comment provided by engineer. */
"Edit selected VM" = "編輯已選擇的虛擬機";

/* No comment provided by engineer. */
"Edit…" = "編輯⋯";

/* No comment provided by engineer. */
"Eject…" = "退出⋯";

/* No comment provided by engineer. */
"Emulated Audio Card" = "模擬聲卡";

/* No comment provided by engineer. */
"Emulated Display Card" = "模擬顯卡";

/* No comment provided by engineer. */
"Emulated Network Card" = "模擬網卡";

/* No comment provided by engineer. */
"Emulated Serial Device" = "模擬序列裝置";

/* No comment provided by engineer. */
"Enable Balloon Device" = "啟用 Balloon 裝置";

/* No comment provided by engineer. */
"Enable Entropy Device" = "啟用 Entropy 裝置";

/* No comment provided by engineer. */
"Enable hardware OpenGL acceleration" = "啟用硬件 OpenGL 加速";

/* No comment provided by engineer. */
"Enable Keyboard" = "啟用鍵盤";

/* No comment provided by engineer. */
"Enable Pointer" = "啟用指標";

/* No comment provided by engineer. */
"Enable Rosetta (x86_64 Emulation)" = "啟用 Rosetta (x86_64 模擬)";

/* No comment provided by engineer. */
"Enable Rosetta on Linux (x86_64 Emulation)" = "在 Linux 中啟用 Rosetta (x86_64 模擬)";

/* No comment provided by engineer. */
"Enable Sound" = "啟用聲音";

/* No comment provided by engineer. */
"Enable UTM Server" = "啟用 UTM 伺服器";

/* No comment provided by engineer. */
"Engine" = "引擎";

/* No comment provided by engineer. */
"Export all arguments as a text file. This is only for debugging purposes as UTM's built-in QEMU differs from upstream QEMU in supported arguments." = "輸出所有引數為一個文字文件。這只用作除錯用途，因為 UTM 的內建 QEMU 在支援的引數上與上游的 QEMU 不同。";

/* No comment provided by engineer. */
"Export Debug Log" = "輸出除錯記錄";

/* No comment provided by engineer. */
"External Drive" = "外部磁碟";

/* UTMData */
"Failed to parse download URL." = "無法解析已下載的 URL。";

/* No comment provided by engineer. */
"Fetch latest Windows installer…" = "取得最新的 Windows 安裝程式⋯";

/* No comment provided by engineer. */
"Font" = "字體";

/* No comment provided by engineer. */
"Force Disable CPU Flags" = "強制停用 CPU 標記";

/* No comment provided by engineer. */
"Force Enable CPU Flags" = "強制啟用 CPU 標記";

/* No comment provided by engineer. */
"Force multicore may improve speed of emulation but also might result in unstable and incorrect emulation." = "強制多核心可能會提高模擬速度，但亦會引致不穩定與不正確的模擬。";

/* No comment provided by engineer. */
"Force PS/2 controller" = "強制使用 PS/2 控制器";

/* No comment provided by engineer. */
"FPS Limit" = "FPS 限制";

/* No comment provided by engineer. */
"Go Back" = "返回";

/* No comment provided by engineer. */
"GPU Acceleration Supported" = "支援 GPU 加速";

/* No comment provided by engineer. */
"Guest Address" = "客户端位址";

/* No comment provided by engineer. */
"Guest Network" = "客户端網絡";

/* No comment provided by engineer. */
"Guest Network (IPv6)" = "客户端網絡 (IPv6)";

/* No comment provided by engineer. */
"Guest Port" = "客户端埠";

/* No comment provided by engineer. */
"Hardware interface on the guest used to mount this image. Different operating systems support different interfaces. The default will be the most common interface." = "用於裝載此映像的客户端硬件介面。不同的作業系統支援不同的介面。預設值將會設定為最常見的介面。";

/* No comment provided by engineer. */
"Hardware OpenGL Acceleration" = "硬件 OpenGL 加速";

/* No comment provided by engineer. */
"Height" = "高度";

/* No comment provided by engineer. */
"Hide" = "隱藏";

/* No comment provided by engineer. */
"Hide dock icon on next launch" = "下次啟動時隱藏 Dock 圖示";

/* No comment provided by engineer. */
"Host Address" = "主機位址";

/* No comment provided by engineer. */
"Host Address (IPv6)" = "主機位址 (IPv6)";

/* No comment provided by engineer. */
"Host Port" = "主機埠";

/* No comment provided by engineer. */
"If checked, no drive image will be stored with the VM. Instead you can mount/unmount image while the VM is running." = "如選擇，將不會儲存磁碟映像檔至虛擬機。然而，你可以在執行虛擬機時裝載/卸除安裝映像。";

/* No comment provided by engineer. */
"If checked, the CPU flag will be enabled. Otherwise, the default value will be used." = "如選擇，將會啟用此 CPU 標記。否則將會使用預設值。";

/* No comment provided by engineer. */
"If checked, the CPU flag will be disabled. Otherwise, the default value will be used." = "如選擇，將會停用此 CPU 標記。否則將會使用預設值。";

/* No comment provided by engineer. */
"If checked, the drive image will be stored with the VM." = "如選擇，磁碟映像檔將會與虛擬機一起儲存。";

/* No comment provided by engineer. */
"If checked, use local time for RTC which is required for Windows. Otherwise, use UTC clock." = "如選擇，將會使用 Windows 要求的 RTC 本地時間。否則將會使用 UTC 時鐘。";

/* VMConfigAppleDriveDetailsView
 VMConfigAppleDriveCreateView*/
"If checked, use NVMe instead of virtio as the disk interface, available on macOS 14+ for Linux guests only. This interface is slower but less likely to encounter filesystem errors." = "如選擇，將會使用 NVMe 而非 virtio 作為磁碟介面，只可在 macOS 14+ 的 Linux 客户端上使用。此介面的速度較慢，但較不易遇到檔案系統錯誤。";

/* No comment provided by engineer. */
"If disabled, the default combination Control+Option (⌃+⌥) will be used." = "如停用，將會使用預設組合鍵 Control + Option (⌃ + ⌥)。";

/* No comment provided by engineer. */
"If enabled, a virtiofs share tagged 'rosetta' will be available on the Linux guest for installing Rosetta for emulating x86_64 on ARM64." = "如啟用，Linux 客户端會有一個標記為「rosetta」的 virtiofs 分享，用於在 ARM64 上安裝 Rosetta 以模擬 x86_64。";

/* No comment provided by engineer. */
"If enabled, any existing screenshot will be deleted the next time the VM is started." = "如啟用，下次啟動虛擬機時，所有現有的螢幕截圖將會刪除。";

/* No comment provided by engineer. */
"If enabled, caps lock will be handled like other keys. If disabled, it is treated as a toggle that is synchronized with the host." = "如啟用，Caps Lock 將會同其他鍵一般處理。如停用，它將會被視為開關鍵，並與主機同步。";

/* No comment provided by engineer. */
"If enabled, input capture will toggle automatically when entering and exiting full screen mode." = "如啟用，在進入和離開全螢幕模式時，將會自動切換輸入擷取。";

/* No comment provided by engineer. */
"If enabled, input capture will toggle automatically when the VM's window is focused." = "如啟用，在聚焦虛擬機視窗時，將自動切換輸入擷取。";

/* No comment provided by engineer. */
"If enabled, num lock will always be on to the guest. Note this may make your keyboard's num lock indicator out of sync." = "如啟用，Num Lock 將會始終對客户端開啟。請注意，這可能會讓鍵盤的 Num Lock 指示器不同步。";

/* No comment provided by engineer. */
"If enabled, Option will be mapped to the Meta key which can be useful for emacs. Otherwise, option will work as the system intended (such as for entering international text)." = "如啟用，Option 鍵將會對應至 Meta 鍵，這對於 Emacs 很有用。否則，Option 鍵將會以系統預設工作 (例如輸入國際文字)。";

/* No comment provided by engineer. */
"If enabled, resizing of the VM window will not be allowed." = "如啟用，將不會允許調整虛擬機視窗的大小。";

/* No comment provided by engineer. */
"If enabled, scroll wheel input will be inverted." = "如啟用，將會反轉滾輪輸入。";

/* No comment provided by engineer. */
"If enabled, the default input devices will be emulated on the USB bus." = "如啟用，預設輸入裝置將會在 USB 匯流排上模擬。";

/* No comment provided by engineer. */
"If set, a frame limit can improve smoothness in rendering by preventing stutters when set to the lowest value your device can handle." = "如設定，當設定為你的裝置可以處理的最低值時，幀限制可以避免卡頓，以提升算圖的平順度。";

/* No comment provided by engineer. */
"If set, boot directly from a raw kernel image and initrd. Otherwise, boot from a supported ISO." = "如設定，直接從 raw 核心映像檔與 initrd 啟動。否則從支援的 ISO 啟動。";

/* No comment provided by engineer. */
"Image Type" = "映像檔種類";

/* No comment provided by engineer. */
"Import Drive…" = "輸入磁碟⋯";

/* No comment provided by engineer. */
"Import VHDX Image" = "輸入 VHDX 映像檔";

/* No comment provided by engineer. */
"Increase the size of the disk image." = "增加磁碟映像檔的大小。";

/* No comment provided by engineer. */
"Initial Ramdisk" = "初始 RAM 磁碟";

/* No comment provided by engineer. */
"Input" = "輸入";

/* No comment provided by engineer. */
"Install drivers and SPICE tools" = "安裝驅動程式與 SPICE 工具";

/* No comment provided by engineer. */
"Install Windows 10 or higher" = "安裝 Windows 10 或更高版本";

/* No comment provided by engineer. */
"Installation Instructions" = "安裝指南";

/* No comment provided by engineer. */
"Instantiate PS/2 controller even when USB input is supported. Required for older Windows." = "即使支援 USB 輸入，仍然實例化 PS/2 控制器。舊版 Windows 需要此功能。";

/* No comment provided by engineer. */
"Interface" = "介面";

/* No comment provided by engineer. */
"IPSW Install Image" = "IPSW 安裝映像檔";

/* No comment provided by engineer. */
"JIT Cache" = "JIT 快取資料";

/* No comment provided by engineer. */
"Kernel" = "核心";

/* No comment provided by engineer. */
"Kernel Image" = "核心映像檔";

/* No comment provided by engineer. */
"Keyboard" = "鍵盤";

/* No comment provided by engineer. */
"Last Seen" = "上次偵測到";

/* No comment provided by engineer. */
"Legacy Hardware" = "舊式硬件";

/* No comment provided by engineer. */
"MAC Address" = "MAC 位址";

/* No comment provided by engineer. */
"Machine" = "電腦";

/* No comment provided by engineer. */
"Maintenance" = "維護";

/* No comment provided by engineer. */
"Mode" = "模式";

/* No comment provided by engineer. */
"Modify settings for this VM." = "變更此虛擬機的設定。";

/* UTMAppleConfigurationDevices */
"Mouse" = "滑鼠";

/* No comment provided by engineer. */
"Move" = "移動";

/* No comment provided by engineer. */
"Move…" = "移動⋯";

/* No comment provided by engineer. */
"Move selected VM" = "移動已選擇的虛擬機";

/* No comment provided by engineer. */
"Move this VM from internal storage to elsewhere." = "將此虛擬機從內置儲存裝置移至其他地方。";

/* No comment provided by engineer. */
"Network" = "網絡";

/* No comment provided by engineer. */
"Network Mode" = "網絡模式";

/* No comment provided by engineer. */
"New Drive…" = "新增磁碟⋯";

/* No comment provided by engineer. */
"New from template…" = "從樣板新增⋯";

/* No comment provided by engineer. */
"New Shared Directory…" = "新增分享目錄⋯";

/* No comment provided by engineer. */
"New VM" = "新增虛擬機";

/* No comment provided by engineer. */
"Older versions of UTM added each IDE device to a separate bus. Check this to change the configuration to place two units on each bus." = "舊版本的 UTM 將每個 IDE 裝置加至單獨的匯流排中。選擇此以變更設定，在每個匯流排上放置兩個單元。";

/* No comment provided by engineer. */
"One Time Donation" = "一次捐贈";

/* No comment provided by engineer. */
"Only available if host architecture matches the target. Otherwise, TCG emulation is used." = "只可以在主機架構與目標相符時使用。否則，將會使用 TCG 模擬。";

/* No comment provided by engineer. */
"Only available on macOS virtual machines." = "只可以在 macOS 虛擬機上使用。";

/* No comment provided by engineer. */
"Only available when Hypervisor is used on supported hardware. TSO speeds up Intel emulation in the guest at the cost of decreased performance in general." = "只可以在支援的硬件上使用 Hypervisor 時使用。TSO 提升了客户端的 Intel 模擬速度，但以總體的效能降低為代價。";

/* No comment provided by engineer. */
"Open VM Settings" = "開啟虛擬機設定";

/* No comment provided by engineer. */
"Optionally select a directory to make accessible inside the VM. Note that support for shared directories varies by the guest operating system and may require additional guest drivers to be installed. See UTM support pages for more details." = "(可留空) 選擇一個目錄，可以在虛擬機中取用。請注意，分享目錄的支援視乎客户端作業系統而定，可能需要安裝額外的客户端驅動程式。有關更多詳細資料，請瀏覽 UTM 支援頁面。";

/* No comment provided by engineer. */
"Options here only apply on next boot and are not saved." = "此處的選項只在下次啟動時生效，且不會儲存。";

/* No comment provided by engineer. */
"Path" = "路徑";

/* No comment provided by engineer. */
"Pointer" = "指標";

/* No comment provided by engineer. */
"Port" = "埠";

/* No comment provided by engineer. */
"Power Off" = "關閉電源";

/* No comment provided by engineer. */
"Prompt" = "提示";

/* No comment provided by engineer. */
"Protocol" = "通訊協定";

/* No comment provided by engineer. */
"QEMU Machine Properties" = "QEMU 電腦屬性";

/* No comment provided by engineer. */
"Quit" = "結束";

/* No comment provided by engineer. */
"RAM" = "記憶體";

/* No comment provided by engineer. */
"Ramdisk (optional)" = "RAM 磁碟 (可留空)";

/* No comment provided by engineer. */
"Random" = "隨機";

/* No comment provided by engineer. */
"Read Only?" = "唯讀？";

/* No comment provided by engineer. */
"Reclaim disk space by re-converting the disk image." = "透過重新轉換以回收磁碟空間。";

/* No comment provided by engineer. */
"Reclaim Space" = "回收空間";

/* No comment provided by engineer. */
"Reject unknown connections by default" = "根據預設拒絕不明連線";

/* No comment provided by engineer. */
"Remove selected shortcut" = "移除已選擇的捷徑";

/* No comment provided by engineer. */
"Renderer Backend" = "算圖器後端";

/* No comment provided by engineer. */
"Require Password" = "需要密碼";

/* No comment provided by engineer. */
"Requires restarting UTM to take affect." = "需要重新啟󠄁動 UTM 以生效。";

/* No comment provided by engineer. */
"Requires SPICE guest agent tools to be installed." = "需要安裝 SPICE 客户端代理程式工具。";

/* No comment provided by engineer. */
"Reset UEFI Variables" = "重設 UEFI 變數";

/* No comment provided by engineer. */
"Resize Console Command" = "調整主控台大小指令";

/* No comment provided by engineer. */
"Resize…" = "調整大小⋯";

/* No comment provided by engineer. */
"Resizing is experimental and could result in data loss. You are strongly encouraged to back-up this VM before proceeding. Would you like to resize to %lld GiB?" = "調整大小為試驗性功能，可能會引致資料遺失。進行前，強烈建議你備份此虛擬機。你想調整大小為 %lld GiB 嗎？";

/* No comment provided by engineer. */
"Resolution" = "解像度";

/* No comment provided by engineer. */
"Restart" = "重新啟動";

/* No comment provided by engineer. */
"Resume" = "繼續";

/* No comment provided by engineer. */
"Resume running VM." = "繼續正在執行的虛擬機。";

/* No comment provided by engineer. */
"Reveal where the VM is stored." = "顯示虛擬機的儲存位置。";

/* No comment provided by engineer. */
"RNG Device" = "RNG 裝置";

/* No comment provided by engineer. */
"Root Image" = "Root 映像檔";

/* No comment provided by engineer. */
"Run" = "執行";

/* No comment provided by engineer. */
"Run Recovery" = "執行「還原模式」";

/* No comment provided by engineer. */
"Run selected VM" = "執行已選擇的虛擬機";

/* No comment provided by engineer. */
"Run the VM in the foreground." = "在螢幕前執行虛擬機。";

/* No comment provided by engineer. */
"Run the VM in the foreground, without saving data changes to disk." = "在螢幕前執行虛擬機，不儲存資料變更至磁碟。";

/* No comment provided by engineer. */
"Run without saving changes" = "執行且不儲存變更";

/* No comment provided by engineer. */
"Section" = "區域";

/* No comment provided by engineer. */
"Secure Boot with TPM 2.0" = "採用 TPM 2.0 的保安啟動";

/* No comment provided by engineer. */
"Select an existing disk image." = "選擇一個現有的磁碟映像。";

/* No comment provided by engineer. */
"Serial" = "序列";

/* No comment provided by engineer. */
"Server Address" = "伺服器位址";

/* No comment provided by engineer. */
"Settings" = "設定";

/* No comment provided by engineer. */
"Share" = "分享";

/* No comment provided by engineer. */
"Share…" = "分享⋯";

/* No comment provided by engineer. */
"Share a copy of this VM and all its data." = "分享此虛擬機及其所有資料的副本。";

/* No comment provided by engineer. */
"Share Directory" = "分享目錄";

/* No comment provided by engineer. */
"Shared Directory: %@" = "已分享目錄：%@";

/* No comment provided by engineer. */
"Share is read only" = "分享為唯讀";

/* No comment provided by engineer. */
"Share selected VM" = "分享已選擇的虛擬機";

/* No comment provided by engineer. */
"Shared Directory Path" = "分享目錄路徑";

/* No comment provided by engineer. */
"Shared Path" = "分享路徑";

/* No comment provided by engineer. */
"Should be off for older operating systems such as Windows 7 or lower." = "對於舊的作業系統應關閉，例如 Windows 7 或更低版本。";

/* No comment provided by engineer. */
"Should be on always unless the guest cannot boot because of this." = "除非客户端因此而無法啟動，否則應始終開啟。";

/* No comment provided by engineer. */
"Show all devices…" = "顯示所有裝置⋯";

/* No comment provided by engineer. */
"Show in Finder" = "在 Finder 中顯示";

/* No comment provided by engineer. */
"Show the main window." = "顯示主視窗。";

/* No comment provided by engineer. */
"Show UTM" = "顯示 UTM";

/* No comment provided by engineer. */
"Show UTM preferences" = "顯示 UTM 偏好設定";

/* No comment provided by engineer. */
"Skip Boot Image" = "略過啟動映像檔";

/* No comment provided by engineer. */
"Skip ISO boot" = "略過 ISO 啟動";

/* No comment provided by engineer. */
"Some older systems do not support UEFI boot, such as Windows 7 and below." = "一些舊版系統不支援 UEFI 啟動，例如 Windows 7 和更低版本。";

/* No comment provided by engineer. */
"Sound" = "聲音";

/* No comment provided by engineer. */
"Sound Backend" = "聲音後端";

/* No comment provided by engineer. */
"Start" = "啟動";

/* No comment provided by engineer. */
"Status" = "狀態";

/* No comment provided by engineer. */
"Stop selected VM" = "停止已選擇的虛擬機";

/* No comment provided by engineer. */
"Stop the running VM." = "停止正在執行的虛擬機。";

/* No comment provided by engineer. */
"Storage" = "儲存空間";

/* No comment provided by engineer. */
"stty cols $COLS rows $ROWS\n" = "stty cols $COLS rows $ROWS\n";

/* No comment provided by engineer. */
"Suspend" = "暫停";

/* No comment provided by engineer. */
"Target" = "目標";

/* No comment provided by engineer. */
"Terminate UTM and stop all running VMs." = "終止 UTM 並停止所有正在執行的虛擬機。";

/* No comment provided by engineer. */
"Text" = "文字";

/* No comment provided by engineer. */
"Text Color" = "文字顏色";

/* No comment provided by engineer. */
"The amount of storage to allocate for this image. Ignored if importing an image. If this is a raw image, then an empty file of this size will be stored with the VM. Otherwise, the disk image will dynamically expand up to this size." = "為此映像檔分配的儲存空間用量。如輸入映像檔，則會忽略。如此為 raw 映像檔，則此大小的空檔案將會與虛擬機一起儲存。否則，磁碟映像檔將會動態擴充至此大小。";

/* No comment provided by engineer. */
"Theme" = "主題";

/* No comment provided by engineer. */
"There are known issues in some newer Linux drivers including black screen, broken compositing, and apps failing to render." = "一些較新的 Linux 驅動程式存在已知問題，當中包括螢幕變黑、顯示合成損毀，以及應用程式無法算圖。";

/* No comment provided by engineer. */
"These are advanced settings affecting QEMU which should be kept default unless you are running into issues." = "這些是影響 QEMU 的進階設定，除非遇到問題，否則你應當保持預設值。";

/* No comment provided by engineer. */
"This is appended to the -machine argument." = "這會加至 -machine 引數的尾端。";

/* No comment provided by engineer. */
"This virtual machine cannot be found at: %@" = "虛擬機無法在此位置找到：%@";

/* No comment provided by engineer. */
"This virtual machine must be re-added to UTM by opening it with Finder. You can find it at the path: %@" = "必須以 Finder 開啟此虛擬機，將其重新加至 UTM。你可以透過此路徑找到它：%@";

/* No comment provided by engineer. */
"TPM 2.0 Device" = "TPM 2.0 裝置";

/* No comment provided by engineer. */
"TPM can be used to protect secrets in the guest operating system. Note that the host will always be able to read these secrets and therefore no expectation of physical security is provided." = "TPM 可以用作保護客户端作業系統中的私密資料。請注意，主機將始終可以讀取這些私密資料，所以無法提供預期的物理保安性。";

/* UTMAppleConfigurationDevices */
"Trackpad" = "觸控板";

/* No comment provided by engineer. */
"Tweaks" = "調整";

/* No comment provided by engineer. */
"Ubuntu Install Guide" = "Ubuntu 安裝指南";

/* No comment provided by engineer. */
"UEFI Boot" = "UEFI 啟動";

/* No comment provided by engineer. */
"Upscaling" = "粗化解像度";

/* No comment provided by engineer. */
"USB Support" = "USB 支援";

/* No comment provided by engineer. */
"Use Apple Virtualization" = "使用 Apple 虛擬化";

/* No comment provided by engineer. */
"Use Hypervisor" = "使用 Hypervisor";

/* No comment provided by engineer. */
"Use local time for base clock" = "將當地時間用作基本時鐘";

/* VMConfigAppleDriveDetailsView
 VMConfigAppleDriveCreateView*/
"Use NVMe Interface" = "使用 NVMe 介面";

/* No comment provided by engineer. */
"Use Rosetta" = "使用 Rosetta";

/* No comment provided by engineer. */
"Use Trackpad" = "使用觸控板";

/* No comment provided by engineer. */
"Use TSO" = "使用 TSO";

/* No comment provided by engineer. */
"Use Virtualization" = "使用虛擬化";

/* No comment provided by engineer. */
"UTM Server" = "UTM 伺服器";

/* No comment provided by engineer. */
"VGA Device RAM (MB)" = "VGA 裝置記憶體 (MB)";

/* No comment provided by engineer. */
"Virtualization" = "虛擬化";

/* No comment provided by engineer. */
"Virtualization Engine" = "虛擬化引擎";

/* No comment provided by engineer. */
"VM display size is fixed" = "固定虛擬機顯示大小";

/* No comment provided by engineer. */
"Wait for Connection" = "等待連線";

/* No comment provided by engineer. */
"WebDAV requires installing SPICE daemon. VirtFS requires installing device drivers." = "WebDAV 需要安裝 SPICE 服務程式。VirtFS 需要安裝裝置驅動程式。";

/* No comment provided by engineer. */
"Width" = "寬度";

/* No comment provided by engineer. */
"Windows Install Guide" = "Windows 安裝指南";

/* No comment provided by engineer. */
"You can use this if your boot options are corrupted or if you wish to re-enroll in the default keys for secure boot." = "如你的引導選項已損毀，或是你希望重新註冊保安啟動的預設鑰匙，可以使用此選項。";

/* No comment provided by engineer. */
"Zoom" = "縮放";
