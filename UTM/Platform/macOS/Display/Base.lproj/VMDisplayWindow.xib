<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.Cocoa.XIB" version="3.0" toolsVersion="17506" targetRuntime="MacOSX.Cocoa" propertyAccessControl="none" useAutolayout="YES" customObjectInstantitationMethod="direct">
    <dependencies>
        <plugIn identifier="com.apple.InterfaceBuilder.CocoaDFRPlugin" version="17008"/>
        <plugIn identifier="com.apple.InterfaceBuilder.CocoaPlugin" version="17506"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
        <capability name="the Touch Bar" minToolsVersion="8.1" minSystemVersion="10.12.2" requiredIntegratedClassName="NSTouchBar"/>
    </dependencies>
    <objects>
        <customObject id="-2" userLabel="File's Owner" customClass="VMDisplayWindowController" customModule="UTM" customModuleProvider="target">
            <connections>
                <outlet property="activityIndicator" destination="Ulx-Td-rrm" id="urA-l7-PXl"/>
                <outlet property="captureMouseToolbarButton" destination="Ge3-wo-FzQ" id="IkH-py-GRd"/>
                <outlet property="captureMouseToolbarItem" destination="FN7-zs-mWC" id="qzI-Kk-0D1"/>
                <outlet property="displayView" destination="M5X-Is-pc9" id="U6s-s4-48i"/>
                <outlet property="drivesToolbarItem" destination="bKL-Th-FFw" id="3SQ-Qt-5jn"/>
                <outlet property="overlayView" destination="nKs-QY-EOf" id="sD4-fu-HIL"/>
                <outlet property="resizeConsoleToolbarItem" destination="Ulf-oT-4cP" id="gIb-1X-LHA"/>
                <outlet property="restartToolbarItem" destination="G7P-HJ-bcy" id="R8T-hV-Gr6"/>
                <outlet property="screenshotView" destination="UUd-qY-POz" id="nMM-Nr-mQo"/>
                <outlet property="sharedFolderToolbarItem" destination="7EC-GE-fIl" id="fz7-Lc-ch7"/>
                <outlet property="startButton" destination="ZTi-Hs-ge6" id="FLE-Yi-azI"/>
                <outlet property="startPauseToolbarItem" destination="kT2-2U-cYm" id="AwM-e3-ge3"/>
                <outlet property="stopToolbarItem" destination="Bkx-Ph-j0D" id="yqL-pR-3gr"/>
                <outlet property="toolbar" destination="qD6-Hj-nyo" id="0iO-Gs-sGk"/>
                <outlet property="touchBar" destination="MdV-I1-RxZ" id="rFp-7N-UJC"/>
                <outlet property="usbToolbarItem" destination="tlw-Fb-ne3" id="LDL-Ug-hcY"/>
                <outlet property="window" destination="QvC-M9-y7g" id="vHh-hj-7eR"/>
                <outlet property="windowsToolbarItem" destination="MQ2-L1-yl7" id="AMz-hQ-2Lu"/>
            </connections>
        </customObject>
        <customObject id="-1" userLabel="First Responder" customClass="FirstResponder"/>
        <customObject id="-3" userLabel="Application" customClass="NSObject"/>
        <window title="UTM" allowsToolTipsWhenApplicationIsInactive="NO" autorecalculatesKeyViewLoop="NO" releasedWhenClosed="NO" animationBehavior="default" toolbarStyle="compact" id="QvC-M9-y7g">
            <windowStyleMask key="styleMask" titled="YES" closable="YES" miniaturizable="YES" resizable="YES"/>
            <windowCollectionBehavior key="collectionBehavior" fullScreenPrimary="YES"/>
            <windowPositionMask key="initialPositionMask" leftStrut="YES" rightStrut="YES" topStrut="YES" bottomStrut="YES"/>
            <rect key="contentRect" x="196" y="240" width="800" height="600"/>
            <rect key="screenRect" x="0.0" y="0.0" width="1728" height="1079"/>
            <view key="contentView" wantsLayer="YES" id="EiT-Mj-1SZ">
                <rect key="frame" x="0.0" y="0.0" width="800" height="600"/>
                <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                <subviews>
                    <visualEffectView blendingMode="behindWindow" material="underWindowBackground" state="followsWindowActiveState" translatesAutoresizingMaskIntoConstraints="NO" id="8hx-jB-ZbR" userLabel="Window Background">
                        <rect key="frame" x="0.0" y="0.0" width="800" height="600"/>
                        <subviews>
                            <customView translatesAutoresizingMaskIntoConstraints="NO" id="M5X-Is-pc9" userLabel="Display View">
                                <rect key="frame" x="0.0" y="0.0" width="800" height="600"/>
                            </customView>
                        </subviews>
                        <constraints>
                            <constraint firstAttribute="trailing" secondItem="M5X-Is-pc9" secondAttribute="trailing" id="BBZ-Ue-n2B"/>
                            <constraint firstAttribute="bottom" secondItem="M5X-Is-pc9" secondAttribute="bottom" id="DmX-Jm-io1"/>
                            <constraint firstItem="M5X-Is-pc9" firstAttribute="top" secondItem="8hx-jB-ZbR" secondAttribute="top" id="QRM-gJ-2Zg"/>
                            <constraint firstItem="M5X-Is-pc9" firstAttribute="leading" secondItem="8hx-jB-ZbR" secondAttribute="leading" id="rWd-DG-2JB"/>
                        </constraints>
                    </visualEffectView>
                    <imageView hidden="YES" horizontalHuggingPriority="251" verticalHuggingPriority="251" horizontalCompressionResistancePriority="250" verticalCompressionResistancePriority="250" translatesAutoresizingMaskIntoConstraints="NO" id="UUd-qY-POz">
                        <rect key="frame" x="0.0" y="0.0" width="800" height="600"/>
                        <imageCell key="cell" refusesFirstResponder="YES" alignment="left" imageScaling="proportionallyDown" id="wtI-uz-Xfv"/>
                    </imageView>
                    <visualEffectView blendingMode="withinWindow" material="dark" state="active" translatesAutoresizingMaskIntoConstraints="NO" id="nKs-QY-EOf" userLabel="Inactive Background">
                        <rect key="frame" x="0.0" y="0.0" width="800" height="600"/>
                        <subviews>
                            <progressIndicator wantsLayer="YES" maxValue="100" displayedWhenStopped="NO" indeterminate="YES" style="spinning" translatesAutoresizingMaskIntoConstraints="NO" id="Ulx-Td-rrm">
                                <rect key="frame" x="384" y="284" width="32" height="32"/>
                            </progressIndicator>
                            <button toolTip="Starts/resumes the VM" translatesAutoresizingMaskIntoConstraints="NO" id="ZTi-Hs-ge6" userLabel="Play Button">
                                <rect key="frame" x="300" y="197" width="200" height="206"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="200" id="dy7-LO-AqQ"/>
                                    <constraint firstAttribute="width" constant="200" id="jeW-Sr-S47"/>
                                </constraints>
                                <buttonCell key="cell" type="square" bezelStyle="shadowlessSquare" image="play.circle.fill" catalog="system" imagePosition="only" alignment="center" imageScaling="proportionallyUpOrDown" inset="2" id="mPs-BZ-Bc0">
                                    <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                    <font key="font" metaFont="system"/>
                                </buttonCell>
                                <connections>
                                    <action selector="startPauseButtonPressed:" target="-2" id="Tst-4i-SvJ"/>
                                </connections>
                            </button>
                        </subviews>
                        <constraints>
                            <constraint firstItem="ZTi-Hs-ge6" firstAttribute="centerY" secondItem="nKs-QY-EOf" secondAttribute="centerY" id="IQD-Gu-RoD"/>
                            <constraint firstItem="ZTi-Hs-ge6" firstAttribute="centerX" secondItem="nKs-QY-EOf" secondAttribute="centerX" id="dQa-7h-o1f"/>
                            <constraint firstItem="Ulx-Td-rrm" firstAttribute="centerX" secondItem="nKs-QY-EOf" secondAttribute="centerX" id="gKZ-pW-k71"/>
                            <constraint firstItem="Ulx-Td-rrm" firstAttribute="centerY" secondItem="nKs-QY-EOf" secondAttribute="centerY" id="sku-gV-yOs"/>
                        </constraints>
                    </visualEffectView>
                </subviews>
                <constraints>
                    <constraint firstAttribute="trailing" secondItem="UUd-qY-POz" secondAttribute="trailing" id="23G-UX-Paa"/>
                    <constraint firstAttribute="trailing" secondItem="nKs-QY-EOf" secondAttribute="trailing" id="2un-XF-OQy"/>
                    <constraint firstAttribute="trailing" secondItem="8hx-jB-ZbR" secondAttribute="trailing" id="HAQ-CZ-ehr"/>
                    <constraint firstAttribute="bottom" secondItem="UUd-qY-POz" secondAttribute="bottom" id="MX4-MB-35G"/>
                    <constraint firstItem="8hx-jB-ZbR" firstAttribute="top" secondItem="EiT-Mj-1SZ" secondAttribute="top" id="OYd-EE-Y2a"/>
                    <constraint firstItem="nKs-QY-EOf" firstAttribute="leading" secondItem="EiT-Mj-1SZ" secondAttribute="leading" id="QRs-fV-dQs"/>
                    <constraint firstItem="UUd-qY-POz" firstAttribute="leading" secondItem="EiT-Mj-1SZ" secondAttribute="leading" id="RKo-Ug-7Tq"/>
                    <constraint firstAttribute="bottom" secondItem="8hx-jB-ZbR" secondAttribute="bottom" id="SU8-eI-KUF"/>
                    <constraint firstItem="UUd-qY-POz" firstAttribute="top" secondItem="EiT-Mj-1SZ" secondAttribute="top" id="VXe-DB-vtS"/>
                    <constraint firstAttribute="bottom" secondItem="nKs-QY-EOf" secondAttribute="bottom" id="aoT-cN-NSa"/>
                    <constraint firstItem="nKs-QY-EOf" firstAttribute="top" secondItem="EiT-Mj-1SZ" secondAttribute="top" id="g12-G5-A0E"/>
                    <constraint firstItem="8hx-jB-ZbR" firstAttribute="leading" secondItem="EiT-Mj-1SZ" secondAttribute="leading" id="vj6-D4-URe"/>
                </constraints>
            </view>
            <toolbar key="toolbar" implicitIdentifier="204ED9EE-2DF0-4A0C-99E3-CD0CC398420A" showsBaselineSeparator="NO" displayMode="iconOnly" sizeMode="regular" id="qD6-Hj-nyo">
                <allowedToolbarItems>
                    <toolbarItem implicitItemIdentifier="9683821E-BFE9-43BA-9AB9-54F930376FD0" label="Stop" paletteLabel="Stop" toolTip="Shuts down and stops the VM" image="power" catalog="system" sizingBehavior="auto" navigational="YES" id="Bkx-Ph-j0D" customClass="NSMenuToolbarItem">
                        <button key="view" verticalHuggingPriority="750" id="FNq-Q0-aIF">
                            <rect key="frame" x="3" y="14" width="27" height="23"/>
                            <autoresizingMask key="autoresizingMask"/>
                            <buttonCell key="cell" type="roundTextured" bezelStyle="texturedRounded" image="power" catalog="system" imagePosition="only" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="CFB-He-ag2">
                                <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                                <font key="font" metaFont="system"/>
                            </buttonCell>
                        </button>
                        <connections>
                            <action selector="stopButtonPressed:" target="-2" id="fmn-4O-3B3"/>
                        </connections>
                    </toolbarItem>
                    <toolbarItem implicitItemIdentifier="49A5F942-A936-49D2-B6AA-AAA5F726396B" label="Start/Pause" paletteLabel="Start/Pause" toolTip="Start/pause the VM" image="play.fill" catalog="system" sizingBehavior="auto" navigational="YES" id="kT2-2U-cYm">
                        <button key="view" verticalHuggingPriority="750" id="0gP-t0-ORQ">
                            <rect key="frame" x="22" y="14" width="24" height="23"/>
                            <autoresizingMask key="autoresizingMask"/>
                            <buttonCell key="cell" type="roundTextured" bezelStyle="texturedRounded" image="play.fill" catalog="system" imagePosition="only" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="sRx-SF-jCQ">
                                <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                                <font key="font" metaFont="system"/>
                            </buttonCell>
                            <connections>
                                <action selector="startPauseButtonPressed:" target="-2" id="geF-sv-ch1"/>
                            </connections>
                        </button>
                    </toolbarItem>
                    <toolbarItem implicitItemIdentifier="FAFCA770-EA16-4713-BAB0-6FAC76767F48" label="Restart" paletteLabel="Restart" toolTip="Restarts the VM" image="restart" catalog="system" sizingBehavior="auto" navigational="YES" id="G7P-HJ-bcy">
                        <button key="view" verticalHuggingPriority="750" id="ES2-tt-K1A">
                            <rect key="frame" x="11" y="14" width="24" height="23"/>
                            <autoresizingMask key="autoresizingMask"/>
                            <buttonCell key="cell" type="roundTextured" bezelStyle="texturedRounded" image="restart" catalog="system" imagePosition="only" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="LA8-B0-6E9">
                                <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                                <font key="font" metaFont="system"/>
                            </buttonCell>
                            <connections>
                                <action selector="restartButtonPressed:" target="-2" id="FF2-ag-6zC"/>
                            </connections>
                        </button>
                    </toolbarItem>
                    <toolbarItem implicitItemIdentifier="E86439F7-239E-4570-B1FE-FFF2B4BA3F10" label="Capture Input" paletteLabel="Capture Input" toolTip="Capture input devices" image="cursorarrow.rays" catalog="system" sizingBehavior="auto" id="FN7-zs-mWC">
                        <button key="view" verticalHuggingPriority="750" id="Ge3-wo-FzQ">
                            <rect key="frame" x="26" y="14" width="28" height="23"/>
                            <autoresizingMask key="autoresizingMask"/>
                            <buttonCell key="cell" type="roundTextured" bezelStyle="texturedRounded" image="cursorarrow.rays" catalog="system" imagePosition="only" alignment="center" alternateImage="command.square" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="EYc-Sr-2L8">
                                <behavior key="behavior" lightByBackground="YES" lightByGray="YES" changeBackground="YES" changeGray="YES"/>
                                <font key="font" metaFont="system"/>
                            </buttonCell>
                            <connections>
                                <action selector="captureMouseButtonPressed:" target="-2" id="St6-3b-MhN"/>
                            </connections>
                        </button>
                    </toolbarItem>
                    <toolbarItem implicitItemIdentifier="1F853CFA-B8C9-429E-A0F4-CE2943B2E9BF" label="Resize Console" paletteLabel="Resize Console" toolTip="Send console resize command" image="arrow.up.left.and.arrow.down.right" catalog="system" sizingBehavior="auto" id="Ulf-oT-4cP">
                        <button key="view" verticalHuggingPriority="750" id="nbx-4W-2pu">
                            <rect key="frame" x="30" y="14" width="28" height="23"/>
                            <autoresizingMask key="autoresizingMask"/>
                            <buttonCell key="cell" type="roundTextured" bezelStyle="texturedRounded" image="arrow.up.left.and.arrow.down.right" catalog="system" imagePosition="only" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="eje-Uj-ltn">
                                <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                                <font key="font" metaFont="system"/>
                            </buttonCell>
                            <connections>
                                <action selector="resizeConsoleButtonPressed:" target="-2" id="APo-3g-su8"/>
                            </connections>
                        </button>
                    </toolbarItem>
                    <toolbarItem implicitItemIdentifier="25B28B29-2844-41D5-A3D4-DD33723B7764" label="USB" paletteLabel="USB" toolTip="USB devices" image="Toolbar USB" sizingBehavior="auto" id="tlw-Fb-ne3">
                        <button key="view" verticalHuggingPriority="750" id="TXS-HH-LSR">
                            <rect key="frame" x="0.0" y="14" width="30" height="23"/>
                            <autoresizingMask key="autoresizingMask"/>
                            <buttonCell key="cell" type="roundTextured" bezelStyle="texturedRounded" image="Toolbar USB" imagePosition="only" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="QSt-cp-DTl">
                                <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                                <font key="font" metaFont="system"/>
                            </buttonCell>
                            <connections>
                                <action selector="usbButtonPressed:" target="-2" id="O2X-6D-raO"/>
                            </connections>
                        </button>
                    </toolbarItem>
                    <toolbarItem implicitItemIdentifier="E26256BC-2B5F-409C-98FF-96C436660BD7" label="Drives" paletteLabel="Drives" toolTip="Drive image options" image="opticaldisc" catalog="system" sizingBehavior="auto" id="bKL-Th-FFw">
                        <button key="view" verticalHuggingPriority="750" id="O9E-id-HIO">
                            <rect key="frame" x="7" y="14" width="27" height="23"/>
                            <autoresizingMask key="autoresizingMask"/>
                            <buttonCell key="cell" type="roundTextured" bezelStyle="texturedRounded" image="opticaldisc" catalog="system" imagePosition="only" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="6ry-MQ-PA2">
                                <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                                <font key="font" metaFont="system"/>
                            </buttonCell>
                            <connections>
                                <action selector="drivesButtonPressed:" target="-2" id="siw-JM-gRH"/>
                            </connections>
                        </button>
                    </toolbarItem>
                    <toolbarItem implicitItemIdentifier="BC7277DA-3080-4525-B162-1DFD21A5B7B1" label="Shared Folder" paletteLabel="Shared Folder" toolTip="Shared folder" image="folder.badge.person.crop" catalog="system" sizingBehavior="auto" id="7EC-GE-fIl">
                        <button key="view" verticalHuggingPriority="750" id="l7R-sT-uoV">
                            <rect key="frame" x="26" y="14" width="30" height="23"/>
                            <autoresizingMask key="autoresizingMask"/>
                            <buttonCell key="cell" type="roundTextured" bezelStyle="texturedRounded" image="folder.badge.person.crop" catalog="system" imagePosition="only" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="s1r-QI-ktp">
                                <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                                <font key="font" metaFont="system"/>
                            </buttonCell>
                            <connections>
                                <action selector="sharedFolderButtonPressed:" target="-2" id="HjG-xe-k07"/>
                            </connections>
                        </button>
                    </toolbarItem>
                    <toolbarItem implicitItemIdentifier="7D4A0EDC-50D2-43DC-81BE-DF49E227BDAD" label="Windows" paletteLabel="Windows" toolTip="Windows" image="rectangle.on.rectangle" catalog="system" bordered="YES" sizingBehavior="auto" id="MQ2-L1-yl7">
                        <button key="view" verticalHuggingPriority="750" id="zHN-gd-wYW">
                            <rect key="frame" x="12" y="14" width="31" height="23"/>
                            <autoresizingMask key="autoresizingMask"/>
                            <buttonCell key="cell" type="roundTextured" bezelStyle="texturedRounded" image="rectangle.on.rectangle" catalog="system" imagePosition="only" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="VmL-Zu-YQ8">
                                <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                                <font key="font" metaFont="system"/>
                            </buttonCell>
                            <connections>
                                <action selector="windowsButtonPressed:" target="-2" id="isC-U7-0qV"/>
                            </connections>
                        </button>
                    </toolbarItem>
                </allowedToolbarItems>
                <defaultToolbarItems>
                    <toolbarItem reference="Bkx-Ph-j0D"/>
                    <toolbarItem reference="kT2-2U-cYm"/>
                    <toolbarItem reference="G7P-HJ-bcy"/>
                    <toolbarItem reference="FN7-zs-mWC"/>
                    <toolbarItem reference="Ulf-oT-4cP"/>
                    <toolbarItem reference="tlw-Fb-ne3"/>
                    <toolbarItem reference="bKL-Th-FFw"/>
                    <toolbarItem reference="7EC-GE-fIl"/>
                    <toolbarItem reference="MQ2-L1-yl7"/>
                </defaultToolbarItems>
            </toolbar>
            <connections>
                <outlet property="delegate" destination="-2" id="aEv-cq-eif"/>
            </connections>
            <point key="canvasLocation" x="-200" y="314"/>
        </window>
        <touchBar id="MdV-I1-RxZ">
            <point key="canvasLocation" x="-199" y="-165"/>
        </touchBar>
        <toolbarItem implicitItemIdentifier="97090EAA-0A04-42C8-A0EF-26D0A5600BE8" label="Toolbar Item" paletteLabel="Toolbar Item" tag="-1" bordered="YES" id="C8Y-BQ-Y6m">
            <size key="minSize" width="24" height="25"/>
            <size key="maxSize" width="24" height="25"/>
        </toolbarItem>
    </objects>
    <resources>
        <image name="Toolbar USB" width="30" height="30"/>
        <image name="arrow.up.left.and.arrow.down.right" catalog="system" width="16" height="15"/>
        <image name="command.square" catalog="system" width="15" height="14"/>
        <image name="cursorarrow.rays" catalog="system" width="16" height="16"/>
        <image name="folder.badge.person.crop" catalog="system" width="19" height="14"/>
        <image name="opticaldisc" catalog="system" width="15" height="15"/>
        <image name="play.circle.fill" catalog="system" width="15" height="15"/>
        <image name="play.fill" catalog="system" width="12" height="13"/>
        <image name="power" catalog="system" width="15" height="16"/>
        <image name="rectangle.on.rectangle" catalog="system" width="19" height="15"/>
        <image name="restart" catalog="system" width="12" height="13"/>
    </resources>
</document>
