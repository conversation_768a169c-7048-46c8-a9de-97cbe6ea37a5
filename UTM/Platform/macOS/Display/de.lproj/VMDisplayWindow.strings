/* Class = "NSToolbarItem"; label = "Shared Folder"; ObjectID = "7EC-GE-fIl"; */
"7EC-GE-fIl.label" = "Freigegebener Ordner";

/* Class = "NSToolbarItem"; paletteLabel = "Shared Folder"; ObjectID = "7EC-GE-fIl"; */
"7EC-GE-fIl.paletteLabel" = "Freigegebener Ordner";

/* Class = "NSToolbarItem"; toolTip = "Shared folder"; ObjectID = "7EC-GE-fIl"; */
"7EC-GE-fIl.toolTip" = "Freigegebener Ordner";

/* Class = "NSToolbarItem"; label = "Drives"; ObjectID = "bKL-Th-FFw"; */
"bKL-Th-FFw.label" = "Laufwerke";

/* Class = "NSToolbarItem"; paletteLabel = "Drives"; ObjectID = "bKL-Th-FFw"; */
"bKL-Th-FFw.paletteLabel" = "Laufwerke";

/* Class = "NSToolbarItem"; toolTip = "Drive image options"; ObjectID = "bKL-Th-FFw"; */
"bKL-Th-FFw.toolTip" = "Optionen zu Laufwerk-Abbildern";

/* Class = "NSToolbarItem"; label = "Stop"; ObjectID = "Bkx-Ph-j0D"; */
"Bkx-Ph-j0D.label" = "Stop";

/* Class = "NSToolbarItem"; paletteLabel = "Stop"; ObjectID = "Bkx-Ph-j0D"; */
"Bkx-Ph-j0D.paletteLabel" = "Stop";

/* Class = "NSToolbarItem"; toolTip = "Shuts down and stops the VM"; ObjectID = "Bkx-Ph-j0D"; */
"Bkx-Ph-j0D.toolTip" = "Beendet die VM abrupt";

/* Class = "NSToolbarItem"; label = "Toolbar Item"; ObjectID = "C8Y-BQ-Y6m"; */
"C8Y-BQ-Y6m.label" = "Menüleisten-Objekt";

/* Class = "NSToolbarItem"; paletteLabel = "Toolbar Item"; ObjectID = "C8Y-BQ-Y6m"; */
"C8Y-BQ-Y6m.paletteLabel" = "Menüleisten-Objekt";

/* Class = "NSToolbarItem"; label = "Capture Mouse"; ObjectID = "FN7-zs-mWC"; */
"FN7-zs-mWC.label" = "Mauszeiger fangen";

/* Class = "NSToolbarItem"; paletteLabel = "Capture Mouse"; ObjectID = "FN7-zs-mWC"; */
"FN7-zs-mWC.paletteLabel" = "Mauszeiger fangen";

/* Class = "NSToolbarItem"; toolTip = "Capture mouse cursor"; ObjectID = "FN7-zs-mWC"; */
"FN7-zs-mWC.toolTip" = "Mauszeiger fangen";

/* Class = "NSToolbarItem"; label = "Restart"; ObjectID = "G7P-HJ-bcy"; */
"G7P-HJ-bcy.label" = "Neustarten";

/* Class = "NSToolbarItem"; paletteLabel = "Restart"; ObjectID = "G7P-HJ-bcy"; */
"G7P-HJ-bcy.paletteLabel" = "Neustarten";

/* Class = "NSToolbarItem"; toolTip = "Restarts the VM"; ObjectID = "G7P-HJ-bcy"; */
"G7P-HJ-bcy.toolTip" = "Startet die VM sofort neu";

/* Class = "NSToolbarItem"; label = "Start/Pause"; ObjectID = "kT2-2U-cYm"; */
"kT2-2U-cYm.label" = "Start/Pause";

/* Class = "NSToolbarItem"; paletteLabel = "Start/Pause"; ObjectID = "kT2-2U-cYm"; */
"kT2-2U-cYm.paletteLabel" = "Start/Pause";

/* Class = "NSToolbarItem"; toolTip = "Start/pause the VM"; ObjectID = "kT2-2U-cYm"; */
"kT2-2U-cYm.toolTip" = "Startet bzw. Pausiert die VM";

/* Class = "NSToolbarItem"; label = "Windows"; ObjectID = "MQ2-L1-yl7"; */
"MQ2-L1-yl7.label" = "Windows";

/* Class = "NSToolbarItem"; paletteLabel = "Windows"; ObjectID = "MQ2-L1-yl7"; */
"MQ2-L1-yl7.paletteLabel" = "Windows";

/* Class = "NSToolbarItem"; toolTip = "Windows"; ObjectID = "MQ2-L1-yl7"; */
"MQ2-L1-yl7.toolTip" = "Windows";

/* Class = "NSWindow"; title = "UTM"; ObjectID = "QvC-M9-y7g"; */
"QvC-M9-y7g.title" = "UTM";

/* Class = "NSToolbarItem"; label = "USB"; ObjectID = "tlw-Fb-ne3"; */
"tlw-Fb-ne3.label" = "USB";

/* Class = "NSToolbarItem"; paletteLabel = "USB"; ObjectID = "tlw-Fb-ne3"; */
"tlw-Fb-ne3.paletteLabel" = "USB";

/* Class = "NSToolbarItem"; toolTip = "USB devices"; ObjectID = "tlw-Fb-ne3"; */
"tlw-Fb-ne3.toolTip" = "USB-Geräte";

/* Class = "NSToolbarItem"; label = "Resize Console"; ObjectID = "Ulf-oT-4cP"; */
"Ulf-oT-4cP.label" = "Größe der Konsole anpassen";

/* Class = "NSToolbarItem"; paletteLabel = "Resize Console"; ObjectID = "Ulf-oT-4cP"; */
"Ulf-oT-4cP.paletteLabel" = "Größe der Konsole anpassen";

/* Class = "NSToolbarItem"; toolTip = "Send console resize command"; ObjectID = "Ulf-oT-4cP"; */
"Ulf-oT-4cP.toolTip" = "Kommando zur Größenänderung senden";

/* Class = "NSButton"; ibShadowedToolTip = "Starts/resumes the VM"; ObjectID = "ZTi-Hs-ge6"; */
"ZTi-Hs-ge6.ibShadowedToolTip" = "Startet die VM bzw. setzt sie fort";

