/* Class = "NSToolbarItem"; label = "Shared Folder"; ObjectID = "7EC-GE-fIl"; */
"7EC-GE-fIl.label" = "共享文件夹";

/* Class = "NSToolbarItem"; paletteLabel = "Shared Folder"; ObjectID = "7EC-GE-fIl"; */
"7EC-GE-fIl.paletteLabel" = "共享文件夹";

/* Class = "NSToolbarItem"; toolTip = "Shared folder"; ObjectID = "7EC-GE-fIl"; */
"7EC-GE-fIl.toolTip" = "共享文件夹";

/* Class = "NSToolbarItem"; label = "Drives"; ObjectID = "bKL-Th-FFw"; */
"bKL-Th-FFw.label" = "驱动器";

/* Class = "NSToolbarItem"; paletteLabel = "Drives"; ObjectID = "bKL-Th-FFw"; */
"bKL-Th-FFw.paletteLabel" = "驱动器";

/* Class = "NSToolbarItem"; toolTip = "Drive image options"; ObjectID = "bKL-Th-FFw"; */
"bKL-Th-FFw.toolTip" = "驱动器映像选项";

/* Class = "NSToolbarItem"; label = "Stop"; ObjectID = "Bkx-Ph-j0D"; */
"Bkx-Ph-j0D.label" = "停止";

/* Class = "NSToolbarItem"; paletteLabel = "Stop"; ObjectID = "Bkx-Ph-j0D"; */
"Bkx-Ph-j0D.paletteLabel" = "停止";

/* Class = "NSToolbarItem"; toolTip = "Shuts down and stops the VM"; ObjectID = "Bkx-Ph-j0D"; */
"Bkx-Ph-j0D.toolTip" = "关闭并停止虚拟机";

/* Class = "NSToolbarItem"; label = "Toolbar Item"; ObjectID = "C8Y-BQ-Y6m"; */
"C8Y-BQ-Y6m.label" = "工具栏项目";

/* Class = "NSToolbarItem"; paletteLabel = "Toolbar Item"; ObjectID = "C8Y-BQ-Y6m"; */
"C8Y-BQ-Y6m.paletteLabel" = "工具栏项目";

/* Class = "NSToolbarItem"; label = "Capture Input"; ObjectID = "FN7-zs-mWC"; */
"FN7-zs-mWC.label" = "捕获输入";

/* Class = "NSToolbarItem"; paletteLabel = "Capture Input"; ObjectID = "FN7-zs-mWC"; */
"FN7-zs-mWC.paletteLabel" = "捕获输入";

/* Class = "NSToolbarItem"; toolTip = "Capture input devices"; ObjectID = "FN7-zs-mWC"; */
"FN7-zs-mWC.toolTip" = "捕获输入设备";

/* Class = "NSToolbarItem"; label = "Restart"; ObjectID = "G7P-HJ-bcy"; */
"G7P-HJ-bcy.label" = "重新启动";

/* Class = "NSToolbarItem"; paletteLabel = "Restart"; ObjectID = "G7P-HJ-bcy"; */
"G7P-HJ-bcy.paletteLabel" = "重新启动";

/* Class = "NSToolbarItem"; toolTip = "Restarts the VM"; ObjectID = "G7P-HJ-bcy"; */
"G7P-HJ-bcy.toolTip" = "重新启动虚拟机";

/* Class = "NSToolbarItem"; label = "Start/Pause"; ObjectID = "kT2-2U-cYm"; */
"kT2-2U-cYm.label" = "启动/暂停";

/* Class = "NSToolbarItem"; paletteLabel = "Start/Pause"; ObjectID = "kT2-2U-cYm"; */
"kT2-2U-cYm.paletteLabel" = "启动/暂停";

/* Class = "NSToolbarItem"; toolTip = "Start/pause the VM"; ObjectID = "kT2-2U-cYm"; */
"kT2-2U-cYm.toolTip" = "启动/暂停虚拟机";

/* Class = "NSToolbarItem"; label = "Windows"; ObjectID = "MQ2-L1-yl7"; */
"MQ2-L1-yl7.label" = "窗口";

/* Class = "NSToolbarItem"; paletteLabel = "Windows"; ObjectID = "MQ2-L1-yl7"; */
"MQ2-L1-yl7.paletteLabel" = "窗口";

/* Class = "NSToolbarItem"; toolTip = "Windows"; ObjectID = "MQ2-L1-yl7"; */
"MQ2-L1-yl7.toolTip" = "窗口";

/* Class = "NSWindow"; title = "UTM"; ObjectID = "QvC-M9-y7g"; */
"QvC-M9-y7g.title" = "UTM";

/* Class = "NSToolbarItem"; label = "USB"; ObjectID = "tlw-Fb-ne3"; */
"tlw-Fb-ne3.label" = "USB";

/* Class = "NSToolbarItem"; paletteLabel = "USB"; ObjectID = "tlw-Fb-ne3"; */
"tlw-Fb-ne3.paletteLabel" = "USB";

/* Class = "NSToolbarItem"; toolTip = "USB devices"; ObjectID = "tlw-Fb-ne3"; */
"tlw-Fb-ne3.toolTip" = "USB 设备";

/* Class = "NSToolbarItem"; label = "Resize Console"; ObjectID = "Ulf-oT-4cP"; */
"Ulf-oT-4cP.label" = "调整控制台大小";

/* Class = "NSToolbarItem"; paletteLabel = "Resize Console"; ObjectID = "Ulf-oT-4cP"; */
"Ulf-oT-4cP.paletteLabel" = "调整控制台大小";

/* Class = "NSToolbarItem"; toolTip = "Send console resize command"; ObjectID = "Ulf-oT-4cP"; */
"Ulf-oT-4cP.toolTip" = "发送调整控制台大小命令";

/* Class = "NSButton"; ibShadowedToolTip = "Starts/resumes the VM"; ObjectID = "ZTi-Hs-ge6"; */
"ZTi-Hs-ge6.ibShadowedToolTip" = "启动/继续运行虚拟机";

