/* Class = "NSToolbarItem"; label = "Shared Folder"; ObjectID = "7EC-GE-fIl"; */
"7EC-GE-fIl.label" = "共享檔案夾";

/* Class = "NSToolbarItem"; paletteLabel = "Shared Folder"; ObjectID = "7EC-GE-fIl"; */
"7EC-GE-fIl.paletteLabel" = "共享檔案夾";

/* Class = "NSToolbarItem"; toolTip = "Shared folder"; ObjectID = "7EC-GE-fIl"; */
"7EC-GE-fIl.toolTip" = "共享檔案夾";

/* Class = "NSToolbarItem"; label = "Drives"; ObjectID = "bKL-Th-FFw"; */
"bKL-Th-FFw.label" = "磁碟機";

/* Class = "NSToolbarItem"; paletteLabel = "Drives"; ObjectID = "bKL-Th-FFw"; */
"bKL-Th-FFw.paletteLabel" = "磁碟機";

/* Class = "NSToolbarItem"; toolTip = "Drive image options"; ObjectID = "bKL-Th-FFw"; */
"bKL-Th-FFw.toolTip" = "磁碟機映像檔選項";

/* Class = "NSToolbarItem"; label = "Stop"; ObjectID = "Bkx-Ph-j0D"; */
"Bkx-Ph-j0D.label" = "停止";

/* Class = "NSToolbarItem"; paletteLabel = "Stop"; ObjectID = "Bkx-Ph-j0D"; */
"Bkx-Ph-j0D.paletteLabel" = "停止";

/* Class = "NSToolbarItem"; toolTip = "Shuts down and stops the VM"; ObjectID = "Bkx-Ph-j0D"; */
"Bkx-Ph-j0D.toolTip" = "關機並停止虛擬機";

/* Class = "NSToolbarItem"; label = "Toolbar Item"; ObjectID = "C8Y-BQ-Y6m"; */
"C8Y-BQ-Y6m.label" = "工具列項目";

/* Class = "NSToolbarItem"; paletteLabel = "Toolbar Item"; ObjectID = "C8Y-BQ-Y6m"; */
"C8Y-BQ-Y6m.paletteLabel" = "工具列項目";

/* Class = "NSToolbarItem"; label = "Capture Input"; ObjectID = "FN7-zs-mWC"; */
"FN7-zs-mWC.label" = "擷取輸入";

/* Class = "NSToolbarItem"; paletteLabel = "Capture Input"; ObjectID = "FN7-zs-mWC"; */
"FN7-zs-mWC.paletteLabel" = "擷取輸入";

/* Class = "NSToolbarItem"; toolTip = "Capture input devices"; ObjectID = "FN7-zs-mWC"; */
"FN7-zs-mWC.toolTip" = "擷取輸入裝置";

/* Class = "NSToolbarItem"; label = "Restart"; ObjectID = "G7P-HJ-bcy"; */
"G7P-HJ-bcy.label" = "重新啟動";

/* Class = "NSToolbarItem"; paletteLabel = "Restart"; ObjectID = "G7P-HJ-bcy"; */
"G7P-HJ-bcy.paletteLabel" = "重新啟動";

/* Class = "NSToolbarItem"; toolTip = "Restarts the VM"; ObjectID = "G7P-HJ-bcy"; */
"G7P-HJ-bcy.toolTip" = "重新啟動虛擬機";

/* Class = "NSToolbarItem"; label = "Start/Pause"; ObjectID = "kT2-2U-cYm"; */
"kT2-2U-cYm.label" = "啟動／暫停";

/* Class = "NSToolbarItem"; paletteLabel = "Start/Pause"; ObjectID = "kT2-2U-cYm"; */
"kT2-2U-cYm.paletteLabel" = "啟動／暫停";

/* Class = "NSToolbarItem"; toolTip = "Start/pause the VM"; ObjectID = "kT2-2U-cYm"; */
"kT2-2U-cYm.toolTip" = "啟動或暫停虛擬機";

/* Class = "NSToolbarItem"; label = "Windows"; ObjectID = "MQ2-L1-yl7"; */
"MQ2-L1-yl7.label" = "視窗";

/* Class = "NSToolbarItem"; paletteLabel = "Windows"; ObjectID = "MQ2-L1-yl7"; */
"MQ2-L1-yl7.paletteLabel" = "視窗";

/* Class = "NSToolbarItem"; toolTip = "Windows"; ObjectID = "MQ2-L1-yl7"; */
"MQ2-L1-yl7.toolTip" = "視窗";

/* Class = "NSWindow"; title = "UTM"; ObjectID = "QvC-M9-y7g"; */
"QvC-M9-y7g.title" = "UTM";

/* Class = "NSToolbarItem"; label = "USB"; ObjectID = "tlw-Fb-ne3"; */
"tlw-Fb-ne3.label" = "USB";

/* Class = "NSToolbarItem"; paletteLabel = "USB"; ObjectID = "tlw-Fb-ne3"; */
"tlw-Fb-ne3.paletteLabel" = "USB";

/* Class = "NSToolbarItem"; toolTip = "USB devices"; ObjectID = "tlw-Fb-ne3"; */
"tlw-Fb-ne3.toolTip" = "USB 裝置";

/* Class = "NSToolbarItem"; label = "Resize Console"; ObjectID = "Ulf-oT-4cP"; */
"Ulf-oT-4cP.label" = "調整主控台大小";

/* Class = "NSToolbarItem"; paletteLabel = "Resize Console"; ObjectID = "Ulf-oT-4cP"; */
"Ulf-oT-4cP.paletteLabel" = "調整主控台大小";

/* Class = "NSToolbarItem"; toolTip = "Send console resize command"; ObjectID = "Ulf-oT-4cP"; */
"Ulf-oT-4cP.toolTip" = "傳送調整主控台大小命令";

/* Class = "NSButton"; ibShadowedToolTip = "Starts/resumes the VM"; ObjectID = "ZTi-Hs-ge6"; */
"ZTi-Hs-ge6.ibShadowedToolTip" = "啟動或繼續虛擬機";

