//
// Copyright © 2021 osy. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

// Configuration settings file format documentation can be found at:
// https://help.apple.com/xcode/#/dev745c5c974

MARKETING_VERSION = 4.7.0
CURRENT_PROJECT_VERSION = 109

// Codesigning settings defined optionally, see Documentation/iOSDevelopment.md
#include? "CodeSigning.xcconfig"

// Entitlements based off of CodeSigning settings
IOS_CODE_SIGN_ENTITLEMENTS_YES = Platform/iOS/iOS.entitlements
IOS_CODE_SIGN_ENTITLEMENTS_NO =
IOS_CODE_SIGN_ENTITLEMENTS = $(IOS_CODE_SIGN_ENTITLEMENTS_$(DEVELOPER_ACCOUNT_PAID:default=NO))
MAC_CODE_SIGN_ENTITLEMENTS_YES = Platform/macOS/macOS.entitlements
MAC_CODE_SIGN_ENTITLEMENTS_NO = Platform/macOS/macOS-unsigned.entitlements
MAC_CODE_SIGN_ENTITLEMENTS = $(MAC_CODE_SIGN_ENTITLEMENTS_$(DEVELOPER_ACCOUNT_VM_ACCESS:default=NO))
HELPER_CODE_SIGN_ENTITLEMENTS_YES = QEMUHelper/QEMUHelper.entitlements
HELPER_CODE_SIGN_ENTITLEMENTS_NO = QEMUHelper/QEMUHelper-unsigned.entitlements
HELPER_CODE_SIGN_ENTITLEMENTS = $(HELPER_CODE_SIGN_ENTITLEMENTS_$(DEVELOPER_ACCOUNT_VM_ACCESS:default=NO))
LAUNCHER_CODE_SIGN_ENTITLEMENTS_YES = QEMULauncher/QEMULauncher.entitlements
LAUNCHER_CODE_SIGN_ENTITLEMENTS_NO = QEMULauncher/QEMULauncher-unsigned.entitlements
LAUNCHER_CODE_SIGN_ENTITLEMENTS = $(LAUNCHER_CODE_SIGN_ENTITLEMENTS_$(DEVELOPER_ACCOUNT_VM_ACCESS:default=NO))
CLI_CODE_SIGN_ENTITLEMENTS_YES = utmctl/utmctl.entitlements
CLI_CODE_SIGN_ENTITLEMENTS_NO = utmctl/utmctl-unsigned.entitlements
CLI_CODE_SIGN_ENTITLEMENTS = $(CLI_CODE_SIGN_ENTITLEMENTS_$(DEVELOPER_ACCOUNT_VM_ACCESS:default=NO))
