{"originHash": "c9482d61e795c27df5a7c772a0750aefc9164d93bd871138a9b229c9f08c6fab", "pins": [{"identity": "altkit", "kind": "remoteSourceControl", "location": "https://github.com/rileytestut/AltKit.git", "state": {"revision": "f799f60ef6fa8b9676b4102b7dfa169fb40b6c92", "version": "0.0.2"}}, {"identity": "cocoaspice", "kind": "remoteSourceControl", "location": "https://github.com/utmapp/CocoaSpice.git", "state": {"branch": "main", "revision": "ac641bd7b88e14b4107dcdb508d9779c49b69617"}}, {"identity": "cod", "kind": "remoteSourceControl", "location": "https://github.com/saagarjha/Cod.git", "state": {"branch": "main", "revision": "c359a08accfb49662a17cdfc5e333c7b4e5c2c56"}}, {"identity": "inappsettingskit", "kind": "remoteSourceControl", "location": "https://github.com/futuretap/InAppSettingsKit.git", "state": {"revision": "2957af57ff10294eaa013d7378a7c95b24aeaddd", "version": "3.4.1"}}, {"identity": "iqkeyboardmanager", "kind": "remoteSourceControl", "location": "https://github.com/hackiftekhar/IQKeyboardManager.git", "state": {"revision": "4dc6bc7d1c9747bd0c261c8c055903f9fdef91f7", "version": "6.5.6"}}, {"identity": "qemukit", "kind": "remoteSourceControl", "location": "https://github.com/utmapp/QEMUKit.git", "state": {"branch": "main", "revision": "6ddf970edaacf708aaf4483d153621921cbf737f"}}, {"identity": "swift-argument-parser", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-argument-parser.git", "state": {"revision": "8f4d2753f0e4778c76d5f05ad16c74f707390531", "version": "1.2.3"}}, {"identity": "swift-log", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-log", "state": {"revision": "532d8b529501fb73a2455b179e0bbb6d49b652ed", "version": "1.5.3"}}, {"identity": "swiftconnect", "kind": "remoteSourceControl", "location": "https://github.com/utmapp/SwiftConnect", "state": {"branch": "main", "revision": "c193f74b804e50deae2f5c036b50c96d66b71fe0"}}, {"identity": "swiftcopyfile", "kind": "remoteSourceControl", "location": "https://github.com/osy/SwiftCopyfile.git", "state": {"branch": "main", "revision": "8495d5eed20daf1e0bb45f9d949f54275a587d66"}}, {"identity": "swiftportmap", "kind": "remoteSourceControl", "location": "https://github.com/osy/SwiftPortmap.git", "state": {"branch": "main", "revision": "72782141ab6f6f6db58bd16bac96d4e7ce901e9a"}}, {"identity": "swiftterm", "kind": "remoteSourceControl", "location": "https://github.com/migueldeicaza/SwiftTerm.git", "state": {"branch": "main", "revision": "ea0f681b25c8385b4a5a48d435e61d11392216e0"}}, {"identity": "swiftui-visual-effects", "kind": "remoteSourceControl", "location": "https://github.com/lucasbrown/swiftui-visual-effects.git", "state": {"revision": "b26f8cebd55ff60ed8953768aa818dfb005b5838", "version": "1.0.3"}}, {"identity": "visionkeyboardkit", "kind": "remoteSourceControl", "location": "https://github.com/utmapp/VisionKeyboardKit.git", "state": {"branch": "main", "revision": "0804e4d64267acc8d08fb23160f5b6ac6134414f"}}, {"identity": "zipfoundation", "kind": "remoteSourceControl", "location": "https://github.com/weichsel/ZIPFoundation.git", "state": {"revision": "a3f5c2bae0f04b0bce9ef3c4ba6bd1031a0564c4", "version": "0.9.17"}}], "version": 3}